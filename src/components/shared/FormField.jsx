import React from 'react';
import styled from 'styled-components';

const FieldContainer = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 14px;
`;

const RequiredIndicator = styled.span`
  color: #dc2626;
  margin-left: 0.25rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;

  &:focus {
    outline: none;
    border-color: #FD8205;
    box-shadow: 0 0 0 3px rgba(253, 130, 5, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }

  ${props => props.hasError && `
    border-color: #dc2626;

    &:focus {
      border-color: #dc2626;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
  `}
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;

  &:focus {
    outline: none;
    border-color: #FD8205;
    box-shadow: 0 0 0 3px rgba(253, 130, 5, 0.1);
  }

  ${props => props.hasError && `
    border-color: #dc2626;

    &:focus {
      border-color: #dc2626;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
  `}
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 14px;
  margin-top: 0.5rem;
`;

const FormField = ({
  label,
  name,
  type = 'text',
  required = false,
  error,
  register,
  placeholder,
  options = []
}) => {
  const hasError = !!error;

  return (
    <FieldContainer>
      <Label htmlFor={name}>
        {label}
        {required && <RequiredIndicator>*</RequiredIndicator>}
      </Label>

      {type === 'select' ? (
        <Select
          id={name}
          hasError={hasError}
          {...register(name)}
        >
          <option value="">Select {label.toLowerCase()}...</option>
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      ) : (
        <Input
          id={name}
          type={type}
          placeholder={placeholder}
          hasError={hasError}
          {...register(name)}
        />
      )}

      {hasError && (
        <ErrorMessage>
          {error.message}
        </ErrorMessage>
      )}
    </FieldContainer>
  );
};

export default FormField;