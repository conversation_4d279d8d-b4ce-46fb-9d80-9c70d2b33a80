[{"/Users/<USER>/Documents/augment-projects/scheduler-admin/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/App.js": "2", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/AuthStatus.jsx": "3", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/apollo/client.js": "4", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/OnboardingWalkthrough.jsx": "5", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/Step1_CreateOrganization.jsx": "6", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/ProgressIndicator.jsx": "7", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/utils/constants.js": "8", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/Step2_AddStaff.jsx": "9", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/Step3_ReviewComplete.jsx": "10", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/utils/validation.js": "11", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/graphql/mutations.js": "12", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/FormField.jsx": "13", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/LoadingSpinner.jsx": "14", "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/hooks/useOnboardingFlow.js": "15"}, {"size": 232, "mtime": 1754601499227, "results": "16", "hashOfConfig": "17"}, {"size": 3322, "mtime": 1754600828985, "results": "18", "hashOfConfig": "17"}, {"size": 3137, "mtime": 1754593387054, "results": "19", "hashOfConfig": "17"}, {"size": 1534, "mtime": 1754593341533, "results": "20", "hashOfConfig": "17"}, {"size": 6112, "mtime": 1754600919090, "results": "21", "hashOfConfig": "17"}, {"size": 5787, "mtime": 1754600133141, "results": "22", "hashOfConfig": "17"}, {"size": 2895, "mtime": 1754592790647, "results": "23", "hashOfConfig": "17"}, {"size": 1608, "mtime": 1754593286173, "results": "24", "hashOfConfig": "17"}, {"size": 8723, "mtime": 1754592838979, "results": "25", "hashOfConfig": "17"}, {"size": 5587, "mtime": 1754598327685, "results": "26", "hashOfConfig": "17"}, {"size": 4141, "mtime": 1754598976402, "results": "27", "hashOfConfig": "17"}, {"size": 1684, "mtime": 1754598910173, "results": "28", "hashOfConfig": "17"}, {"size": 2430, "mtime": 1754592812276, "results": "29", "hashOfConfig": "17"}, {"size": 798, "mtime": 1754592874246, "results": "30", "hashOfConfig": "17"}, {"size": 1825, "mtime": 1754589599138, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkma60", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/App.js", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/AuthStatus.jsx", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/apollo/client.js", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/OnboardingWalkthrough.jsx", ["77", "78", "79", "80", "81"], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/Step1_CreateOrganization.jsx", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/ProgressIndicator.jsx", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/utils/constants.js", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/Step2_AddStaff.jsx", ["82"], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/Step3_ReviewComplete.jsx", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/utils/validation.js", ["83"], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/graphql/mutations.js", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/FormField.jsx", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/components/shared/LoadingSpinner.jsx", [], [], "/Users/<USER>/Documents/augment-projects/scheduler-admin/src/hooks/useOnboardingFlow.js", [], [], {"ruleId": "84", "severity": 1, "message": "85", "line": 83, "column": 5, "nodeType": "86", "messageId": "87", "endLine": 83, "endColumn": 21}, {"ruleId": "88", "severity": 1, "message": "89", "line": 119, "column": 6, "nodeType": "90", "endLine": 119, "endColumn": 8, "suggestions": "91"}, {"ruleId": "92", "severity": 1, "message": "93", "line": 195, "column": 11, "nodeType": "94", "messageId": "95", "endLine": 198, "endColumn": 13}, {"ruleId": "92", "severity": 1, "message": "96", "line": 203, "column": 11, "nodeType": "94", "messageId": "95", "endLine": 210, "endColumn": 13}, {"ruleId": "92", "severity": 1, "message": "97", "line": 215, "column": 11, "nodeType": "94", "messageId": "95", "endLine": 219, "endColumn": 13}, {"ruleId": "84", "severity": 1, "message": "98", "line": 216, "column": 9, "nodeType": "86", "messageId": "87", "endLine": 216, "endColumn": 25}, {"ruleId": "99", "severity": 1, "message": "100", "line": 4, "column": 23, "nodeType": "101", "messageId": "102", "endLine": 4, "endColumn": 24, "suggestions": "103"}, "no-unused-vars", "'canProceedToStep' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'addStaffMember', 'goToStep', 'setOrganizationId', and 'updateOrganizationData'. Either include them or remove the dependency array.", "ArrayExpression", ["104"], "react/jsx-pascal-case", "Imported JSX component Step1_CreateOrganization must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "Imported JSX component Step2_AddStaff must be in PascalCase or SCREAMING_SNAKE_CASE", "Imported JSX component Step3_ReviewComplete must be in PascalCase or SCREAMING_SNAKE_CASE", "'handleAddAnother' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["105", "106"], {"desc": "107", "fix": "108"}, {"messageId": "109", "fix": "110", "desc": "111"}, {"messageId": "112", "fix": "113", "desc": "114"}, "Update the dependencies array to be: [addStaffMember, goToStep, setOrganizationId, updateOrganizationData]", {"range": "115", "text": "116"}, "removeEscape", {"range": "117", "text": "118"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "119", "text": "120"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", [2831, 2833], "[addStaffMember, goToStep, setOrganizationId, updateOrganizationData]", [84, 85], "", [84, 84], "\\"]