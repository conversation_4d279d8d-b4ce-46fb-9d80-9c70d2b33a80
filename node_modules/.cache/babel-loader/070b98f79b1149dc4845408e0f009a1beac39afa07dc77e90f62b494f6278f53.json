{"ast": null, "code": "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport function validateOperation(operation) {\n  var OPERATION_FIELDS = [\"query\", \"operationName\", \"variables\", \"extensions\", \"context\"];\n  for (var _i = 0, _a = Object.keys(operation); _i < _a.length; _i++) {\n    var key = _a[_i];\n    if (OPERATION_FIELDS.indexOf(key) < 0) {\n      throw newInvariantError(46, key);\n    }\n  }\n  return operation;\n}", "map": {"version": 3, "names": ["newInvariantError", "validateOperation", "operation", "OPERATION_FIELDS", "_i", "_a", "Object", "keys", "length", "key", "indexOf"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/link/utils/validateOperation.ts"], "sourcesContent": ["import { newInvariantError } from \"../../utilities/globals/index.js\";\nimport type { GraphQLRequest } from \"../core/index.js\";\n\nexport function validateOperation(operation: GraphQLRequest): GraphQLRequest {\n  const OPERATION_FIELDS = [\n    \"query\",\n    \"operationName\",\n    \"variables\",\n    \"extensions\",\n    \"context\",\n  ];\n  for (let key of Object.keys(operation)) {\n    if (OPERATION_FIELDS.indexOf(key) < 0) {\n      throw newInvariantError(`illegal argument: %s`, key);\n    }\n  }\n\n  return operation;\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kCAAkC;AAGpE,OAAM,SAAUC,iBAAiBA,CAACC,SAAyB;EACzD,IAAMC,gBAAgB,GAAG,CACvB,OAAO,EACP,eAAe,EACf,WAAW,EACX,YAAY,EACZ,SAAS,CACV;EACD,KAAgB,IAAAC,EAAA,IAAsB,EAAtBC,EAAA,GAAAC,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,EAAtBE,EAAA,GAAAC,EAAA,CAAAG,MAAsB,EAAtBJ,EAAA,EAAsB,EAAE;IAAnC,IAAIK,GAAG,GAAAJ,EAAA,CAAAD,EAAA;IACV,IAAID,gBAAgB,CAACO,OAAO,CAACD,GAAG,CAAC,GAAG,CAAC,EAAE;MACrC,MAAMT,iBAAiB,CAAC,IAAAS,GAAA;IAC1B;EACF;EAEA,OAAOP,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}