{"ast": null, "code": "import { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\nexport function graphQLResultHasError(result) {\n  var errors = getGraphQLErrorsFromResult(result);\n  return isNonEmptyArray(errors);\n}\nexport function getGraphQLErrorsFromResult(result) {\n  var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n  if (isExecutionPatchIncrementalResult(result) && isNonEmptyArray(result.incremental)) {\n    result.incremental.forEach(function (incrementalResult) {\n      if (incrementalResult.errors) {\n        graphQLErrors.push.apply(graphQLErrors, incrementalResult.errors);\n      }\n    });\n  }\n  return graphQLErrors;\n}", "map": {"version": 3, "names": ["isNonEmptyArray", "isExecutionPatchIncrementalResult", "graphQLResultHasError", "result", "errors", "getGraphQLErrorsFromResult", "graphQLErrors", "slice", "incremental", "for<PERSON>ach", "incrementalResult", "push", "apply"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/utilities/common/errorHandling.ts"], "sourcesContent": ["import type { FetchResult } from \"../../link/core/index.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\n\nexport function graphQLResultHasError<T>(result: FetchResult<T>): boolean {\n  const errors = getGraphQLErrorsFromResult(result);\n  return isNonEmptyArray(errors);\n}\n\nexport function getGraphQLErrorsFromResult<T>(result: FetchResult<T>) {\n  const graphQLErrors =\n    isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n\n  if (\n    isExecutionPatchIncrementalResult(result) &&\n    isNonEmptyArray(result.incremental)\n  ) {\n    result.incremental.forEach((incrementalResult) => {\n      if (incrementalResult.errors) {\n        graphQLErrors.push(...incrementalResult.errors);\n      }\n    });\n  }\n  return graphQLErrors;\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,aAAa;AAC7C,SAASC,iCAAiC,QAAQ,wBAAwB;AAE1E,OAAM,SAAUC,qBAAqBA,CAAIC,MAAsB;EAC7D,IAAMC,MAAM,GAAGC,0BAA0B,CAACF,MAAM,CAAC;EACjD,OAAOH,eAAe,CAACI,MAAM,CAAC;AAChC;AAEA,OAAM,SAAUC,0BAA0BA,CAAIF,MAAsB;EAClE,IAAMG,aAAa,GACjBN,eAAe,CAACG,MAAM,CAACC,MAAM,CAAC,GAAGD,MAAM,CAACC,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EAE9D,IACEN,iCAAiC,CAACE,MAAM,CAAC,IACzCH,eAAe,CAACG,MAAM,CAACK,WAAW,CAAC,EACnC;IACAL,MAAM,CAACK,WAAW,CAACC,OAAO,CAAC,UAACC,iBAAiB;MAC3C,IAAIA,iBAAiB,CAACN,MAAM,EAAE;QAC5BE,aAAa,CAACK,IAAI,CAAAC,KAAA,CAAlBN,aAAa,EAASI,iBAAiB,CAACN,MAAM;MAChD;IACF,CAAC,CAAC;EACJ;EACA,OAAOE,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}