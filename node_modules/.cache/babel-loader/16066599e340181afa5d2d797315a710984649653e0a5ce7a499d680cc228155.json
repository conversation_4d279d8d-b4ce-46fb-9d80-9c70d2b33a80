{"ast": null, "code": "\"use strict\";\n\nif (0) {\n  // Trick cjs-module-lexer into adding named exports for all React exports.\n  // (if imported with `import()`, they will appear in `.default` as well.)\n  // This way, cjs-module-lexer will let all of react's (named) exports through unchanged.\n  module.exports = require(\"react\");\n}\n// We don't want bundlers to error when they encounter usage of any of these exports.\n// It's up to the package author to ensure that if they access React internals,\n// they do so in a safe way that won't break if <PERSON>act changes how they use these internals.\n// (e.g. only access them in development, and only in an optional way that won't\n// break if internals are not there or do not have the expected structure)\n// @ts-ignore\nmodule.exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = undefined;\n// @ts-ignore\nmodule.exports.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = undefined;\n// @ts-ignore\nmodule.exports.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = undefined;\n// Here we actually pull in the React library and add everything\n// it exports to our own `module.exports`.\n// If React suddenly were to add one of the above \"polyfilled\" exports,\n// the React version would overwrite our version, so this should be\n// future-proof.\nObject.assign(module.exports, require(\"react\"));", "map": {"version": 3, "names": ["module", "exports", "require", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "undefined", "__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "Object", "assign"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/rehackt/index.js"], "sourcesContent": ["\"use strict\";\nif (0) {\n  // Trick cjs-module-lexer into adding named exports for all React exports.\n  // (if imported with `import()`, they will appear in `.default` as well.)\n  // This way, cjs-module-lexer will let all of react's (named) exports through unchanged.\n  module.exports = require(\"react\");\n}\n// We don't want bundlers to error when they encounter usage of any of these exports.\n// It's up to the package author to ensure that if they access React internals,\n// they do so in a safe way that won't break if <PERSON>act changes how they use these internals.\n// (e.g. only access them in development, and only in an optional way that won't\n// break if internals are not there or do not have the expected structure)\n// @ts-ignore\nmodule.exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = undefined;\n// @ts-ignore\nmodule.exports.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = undefined;\n// @ts-ignore\nmodule.exports.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = undefined;\n// Here we actually pull in the React library and add everything\n// it exports to our own `module.exports`.\n// If React suddenly were to add one of the above \"polyfilled\" exports,\n// the React version would overwrite our version, so this should be\n// future-proof.\nObject.assign(module.exports, require(\"react\"));\n"], "mappings": "AAAA,YAAY;;AACZ,IAAI,CAAC,EAAE;EACL;EACA;EACA;EACAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,OAAO,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,MAAM,CAACC,OAAO,CAACE,kDAAkD,GAAGC,SAAS;AAC7E;AACAJ,MAAM,CAACC,OAAO,CAACI,+DAA+D,GAAGD,SAAS;AAC1F;AACAJ,MAAM,CAACC,OAAO,CAACK,+DAA+D,GAAGF,SAAS;AAC1F;AACA;AACA;AACA;AACA;AACAG,MAAM,CAACC,MAAM,CAACR,MAAM,CAACC,OAAO,EAAEC,OAAO,CAAC,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}