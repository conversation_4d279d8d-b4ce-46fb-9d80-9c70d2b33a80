{"ast": null, "code": "import { dep, Slot } from \"optimism\";\n// Contextual Slot that acquires its value when custom read functions are\n// called in Policies#readField.\nexport var cacheSlot = new Slot();\nvar cacheInfoMap = new WeakMap();\nfunction getCacheInfo(cache) {\n  var info = cacheInfoMap.get(cache);\n  if (!info) {\n    cacheInfoMap.set(cache, info = {\n      vars: new Set(),\n      dep: dep()\n    });\n  }\n  return info;\n}\nexport function forgetCache(cache) {\n  getCacheInfo(cache).vars.forEach(function (rv) {\n    return rv.forgetCache(cache);\n  });\n}\n// Calling forgetCache(cache) serves to silence broadcasts and allows the\n// cache to be garbage collected. However, the varsByCache WeakMap\n// preserves the set of reactive variables that were previously associated\n// with this cache, which makes it possible to \"recall\" the cache at a\n// later time, by reattaching it to those variables. If the cache has been\n// garbage collected in the meantime, because it is no longer reachable,\n// you won't be able to call recallCache(cache), and the cache will\n// automatically disappear from the varsByCache WeakMap.\nexport function recallCache(cache) {\n  getCacheInfo(cache).vars.forEach(function (rv) {\n    return rv.attachCache(cache);\n  });\n}\nexport function makeVar(value) {\n  var caches = new Set();\n  var listeners = new Set();\n  var rv = function (newValue) {\n    if (arguments.length > 0) {\n      if (value !== newValue) {\n        value = newValue;\n        caches.forEach(function (cache) {\n          // Invalidate any fields with custom read functions that\n          // consumed this variable, so query results involving those\n          // fields will be recomputed the next time we read them.\n          getCacheInfo(cache).dep.dirty(rv);\n          // Broadcast changes to any caches that have previously read\n          // from this variable.\n          broadcast(cache);\n        });\n        // Finally, notify any listeners added via rv.onNextChange.\n        var oldListeners = Array.from(listeners);\n        listeners.clear();\n        oldListeners.forEach(function (listener) {\n          return listener(value);\n        });\n      }\n    } else {\n      // When reading from the variable, obtain the current cache from\n      // context via cacheSlot. This isn't entirely foolproof, but it's\n      // the same system that powers varDep.\n      var cache = cacheSlot.getValue();\n      if (cache) {\n        attach(cache);\n        getCacheInfo(cache).dep(rv);\n      }\n    }\n    return value;\n  };\n  rv.onNextChange = function (listener) {\n    listeners.add(listener);\n    return function () {\n      listeners.delete(listener);\n    };\n  };\n  var attach = rv.attachCache = function (cache) {\n    caches.add(cache);\n    getCacheInfo(cache).vars.add(rv);\n    return rv;\n  };\n  rv.forgetCache = function (cache) {\n    return caches.delete(cache);\n  };\n  return rv;\n}\nfunction broadcast(cache) {\n  if (cache.broadcastWatches) {\n    cache.broadcastWatches();\n  }\n}", "map": {"version": 3, "names": ["dep", "Slot", "cacheSlot", "cacheInfoMap", "WeakMap", "getCacheInfo", "cache", "info", "get", "set", "vars", "Set", "forgetCache", "for<PERSON>ach", "rv", "recallCache", "attachCache", "makeVar", "value", "caches", "listeners", "newValue", "arguments", "length", "dirty", "broadcast", "oldListeners", "Array", "from", "clear", "listener", "getValue", "attach", "onNextChange", "add", "delete", "broadcastWatches"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/cache/inmemory/reactiveVars.ts"], "sourcesContent": ["import type { OptimisticDependencyFunction } from \"optimism\";\nimport { dep, Slot } from \"optimism\";\nimport type { InMemoryCache } from \"./inMemoryCache.js\";\nimport type { ApolloCache } from \"../../core/index.js\";\n\nexport interface ReactiveVar<T> {\n  (newValue?: T): T;\n  onNextChange(listener: ReactiveListener<T>): () => void;\n  attachCache(cache: ApolloCache<any>): this;\n  forgetCache(cache: ApolloCache<any>): boolean;\n}\n\nexport type ReactiveListener<T> = (value: T) => any;\n\n// Contextual Slot that acquires its value when custom read functions are\n// called in Policies#readField.\nexport const cacheSlot = new Slot<ApolloCache<any>>();\n\nconst cacheInfoMap = new WeakMap<\n  ApolloCache<any>,\n  {\n    vars: Set<ReactiveVar<any>>;\n    dep: OptimisticDependencyFunction<ReactiveVar<any>>;\n  }\n>();\n\nfunction getCacheInfo(cache: ApolloCache<any>) {\n  let info = cacheInfoMap.get(cache)!;\n  if (!info) {\n    cacheInfoMap.set(\n      cache,\n      (info = {\n        vars: new Set(),\n        dep: dep(),\n      })\n    );\n  }\n  return info;\n}\n\nexport function forgetCache(cache: ApolloCache<any>) {\n  getCacheInfo(cache).vars.forEach((rv) => rv.forgetCache(cache));\n}\n\n// Calling forgetCache(cache) serves to silence broadcasts and allows the\n// cache to be garbage collected. However, the varsByCache WeakMap\n// preserves the set of reactive variables that were previously associated\n// with this cache, which makes it possible to \"recall\" the cache at a\n// later time, by reattaching it to those variables. If the cache has been\n// garbage collected in the meantime, because it is no longer reachable,\n// you won't be able to call recallCache(cache), and the cache will\n// automatically disappear from the varsByCache WeakMap.\nexport function recallCache(cache: ApolloCache<any>) {\n  getCacheInfo(cache).vars.forEach((rv) => rv.attachCache(cache));\n}\n\nexport function makeVar<T>(value: T): ReactiveVar<T> {\n  const caches = new Set<ApolloCache<any>>();\n  const listeners = new Set<ReactiveListener<T>>();\n\n  const rv: ReactiveVar<T> = function (newValue) {\n    if (arguments.length > 0) {\n      if (value !== newValue) {\n        value = newValue!;\n        caches.forEach((cache) => {\n          // Invalidate any fields with custom read functions that\n          // consumed this variable, so query results involving those\n          // fields will be recomputed the next time we read them.\n          getCacheInfo(cache).dep.dirty(rv);\n          // Broadcast changes to any caches that have previously read\n          // from this variable.\n          broadcast(cache);\n        });\n        // Finally, notify any listeners added via rv.onNextChange.\n        const oldListeners = Array.from(listeners);\n        listeners.clear();\n        oldListeners.forEach((listener) => listener(value));\n      }\n    } else {\n      // When reading from the variable, obtain the current cache from\n      // context via cacheSlot. This isn't entirely foolproof, but it's\n      // the same system that powers varDep.\n      const cache = cacheSlot.getValue();\n      if (cache) {\n        attach(cache);\n        getCacheInfo(cache).dep(rv);\n      }\n    }\n\n    return value;\n  };\n\n  rv.onNextChange = (listener) => {\n    listeners.add(listener);\n    return () => {\n      listeners.delete(listener);\n    };\n  };\n\n  const attach = (rv.attachCache = (cache) => {\n    caches.add(cache);\n    getCacheInfo(cache).vars.add(rv);\n    return rv;\n  });\n\n  rv.forgetCache = (cache) => caches.delete(cache);\n\n  return rv;\n}\n\ntype Broadcastable = ApolloCache<any> & {\n  // This method is protected in InMemoryCache, which we are ignoring, but\n  // we still want some semblance of type safety when we call it.\n  broadcastWatches?: InMemoryCache[\"broadcastWatches\"];\n};\n\nfunction broadcast(cache: Broadcastable) {\n  if (cache.broadcastWatches) {\n    cache.broadcastWatches();\n  }\n}\n"], "mappings": "AACA,SAASA,GAAG,EAAEC,IAAI,QAAQ,UAAU;AAapC;AACA;AACA,OAAO,IAAMC,SAAS,GAAG,IAAID,IAAI,EAAoB;AAErD,IAAME,YAAY,GAAG,IAAIC,OAAO,EAM7B;AAEH,SAASC,YAAYA,CAACC,KAAuB;EAC3C,IAAIC,IAAI,GAAGJ,YAAY,CAACK,GAAG,CAACF,KAAK,CAAE;EACnC,IAAI,CAACC,IAAI,EAAE;IACTJ,YAAY,CAACM,GAAG,CACdH,KAAK,EACJC,IAAI,GAAG;MACNG,IAAI,EAAE,IAAIC,GAAG,EAAE;MACfX,GAAG,EAAEA,GAAG;KACR,CACH;EACH;EACA,OAAOO,IAAI;AACb;AAEA,OAAM,SAAUK,WAAWA,CAACN,KAAuB;EACjDD,YAAY,CAACC,KAAK,CAAC,CAACI,IAAI,CAACG,OAAO,CAAC,UAACC,EAAE;IAAK,OAAAA,EAAE,CAACF,WAAW,CAACN,KAAK,CAAC;EAArB,CAAqB,CAAC;AACjE;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAM,SAAUS,WAAWA,CAACT,KAAuB;EACjDD,YAAY,CAACC,KAAK,CAAC,CAACI,IAAI,CAACG,OAAO,CAAC,UAACC,EAAE;IAAK,OAAAA,EAAE,CAACE,WAAW,CAACV,KAAK,CAAC;EAArB,CAAqB,CAAC;AACjE;AAEA,OAAM,SAAUW,OAAOA,CAAIC,KAAQ;EACjC,IAAMC,MAAM,GAAG,IAAIR,GAAG,EAAoB;EAC1C,IAAMS,SAAS,GAAG,IAAIT,GAAG,EAAuB;EAEhD,IAAMG,EAAE,GAAmB,SAAAA,CAAUO,QAAQ;IAC3C,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACxB,IAAIL,KAAK,KAAKG,QAAQ,EAAE;QACtBH,KAAK,GAAGG,QAAS;QACjBF,MAAM,CAACN,OAAO,CAAC,UAACP,KAAK;UACnB;UACA;UACA;UACAD,YAAY,CAACC,KAAK,CAAC,CAACN,GAAG,CAACwB,KAAK,CAACV,EAAE,CAAC;UACjC;UACA;UACAW,SAAS,CAACnB,KAAK,CAAC;QAClB,CAAC,CAAC;QACF;QACA,IAAMoB,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACR,SAAS,CAAC;QAC1CA,SAAS,CAACS,KAAK,EAAE;QACjBH,YAAY,CAACb,OAAO,CAAC,UAACiB,QAAQ;UAAK,OAAAA,QAAQ,CAACZ,KAAK,CAAC;QAAf,CAAe,CAAC;MACrD;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA,IAAMZ,KAAK,GAAGJ,SAAS,CAAC6B,QAAQ,EAAE;MAClC,IAAIzB,KAAK,EAAE;QACT0B,MAAM,CAAC1B,KAAK,CAAC;QACbD,YAAY,CAACC,KAAK,CAAC,CAACN,GAAG,CAACc,EAAE,CAAC;MAC7B;IACF;IAEA,OAAOI,KAAK;EACd,CAAC;EAEDJ,EAAE,CAACmB,YAAY,GAAG,UAACH,QAAQ;IACzBV,SAAS,CAACc,GAAG,CAACJ,QAAQ,CAAC;IACvB,OAAO;MACLV,SAAS,CAACe,MAAM,CAACL,QAAQ,CAAC;IAC5B,CAAC;EACH,CAAC;EAED,IAAME,MAAM,GAAIlB,EAAE,CAACE,WAAW,GAAG,UAACV,KAAK;IACrCa,MAAM,CAACe,GAAG,CAAC5B,KAAK,CAAC;IACjBD,YAAY,CAACC,KAAK,CAAC,CAACI,IAAI,CAACwB,GAAG,CAACpB,EAAE,CAAC;IAChC,OAAOA,EAAE;EACX,CAAE;EAEFA,EAAE,CAACF,WAAW,GAAG,UAACN,KAAK;IAAK,OAAAa,MAAM,CAACgB,MAAM,CAAC7B,KAAK,CAAC;EAApB,CAAoB;EAEhD,OAAOQ,EAAE;AACX;AAQA,SAASW,SAASA,CAACnB,KAAoB;EACrC,IAAIA,KAAK,CAAC8B,gBAAgB,EAAE;IAC1B9B,KAAK,CAAC8B,gBAAgB,EAAE;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}