{"ast": null, "code": "export { AutoCleanedStrongCache, AutoCleanedWeakCache } from \"./caches.js\";\nexport { cacheSizes } from \"./sizes.js\";", "map": {"version": 3, "names": ["AutoCleanedStrongCache", "AutoCleanedWeakCache", "cacheSizes"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/utilities/caching/index.ts"], "sourcesContent": ["export { AutoCleanedStrongCache, AutoCleanedWeakCache } from \"./caches.js\";\nexport type { CacheSizes } from \"./sizes.js\";\nexport { cacheSizes, defaultCacheSizes } from \"./sizes.js\";\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,oBAAoB,QAAQ,aAAa;AAE1E,SAASC,UAAU,QAA2B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}