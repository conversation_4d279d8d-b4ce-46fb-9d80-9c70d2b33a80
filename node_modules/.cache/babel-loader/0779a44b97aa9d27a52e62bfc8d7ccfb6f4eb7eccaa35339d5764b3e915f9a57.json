{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\nfunction isNodeResponse(value) {\n  return !!value.body;\n}\nfunction isReadableStream(value) {\n  return !!value.getReader;\n}\nfunction isAsyncIterableIterator(value) {\n  return !!(canUseAsyncIteratorSymbol && value[Symbol.asyncIterator]);\n}\nfunction isStreamableBlob(value) {\n  return !!value.stream;\n}\nfunction isBlob(value) {\n  return !!value.arrayBuffer;\n}\nfunction isNodeReadableStream(value) {\n  return !!value.pipe;\n}\nexport function responseIterator(response) {\n  var body = response;\n  if (isNodeResponse(response)) body = response.body;\n  if (isAsyncIterableIterator(body)) return asyncIterator(body);\n  if (isReadableStream(body)) return readerIterator(body.getReader());\n  // this errors without casting to ReadableStream<T>\n  // because Blob.stream() returns a NodeJS ReadableStream\n  if (isStreamableBlob(body)) {\n    return readerIterator(body.stream().getReader());\n  }\n  if (isBlob(body)) return promiseIterator(body.arrayBuffer());\n  if (isNodeReadableStream(body)) return nodeStreamIterator(body);\n  throw new Error(\"Unknown body type for responseIterator. Please pass a streamable response.\");\n}", "map": {"version": 3, "names": ["canUseAsyncIteratorSymbol", "asyncIterator", "nodeStreamIterator", "promiseIterator", "readerIterator", "isNodeResponse", "value", "body", "isReadableStream", "<PERSON><PERSON><PERSON><PERSON>", "isAsyncIterableIterator", "Symbol", "isStreamableBlob", "stream", "isBlob", "arrayBuffer", "isNodeReadableStream", "pipe", "responseIterator", "response", "Error"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/link/http/responseIterator.ts"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\n\nimport type { Response as NodeResponse } from \"node-fetch\";\nimport type { Readable as NodeReadableStream } from \"stream\";\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\n\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\n\nfunction isNodeResponse(value: any): value is NodeResponse {\n  return !!(value as NodeResponse).body;\n}\n\nfunction isReadableStream(value: any): value is ReadableStream<any> {\n  return !!(value as ReadableStream<any>).getReader;\n}\n\nfunction isAsyncIterableIterator(\n  value: any\n): value is AsyncIterableIterator<any> {\n  return !!(\n    canUseAsyncIteratorSymbol &&\n    (value as AsyncIterableIterator<any>)[Symbol.asyncIterator]\n  );\n}\n\nfunction isStreamableBlob(value: any): value is Blob {\n  return !!(value as Blob).stream;\n}\n\nfunction isBlob(value: any): value is Blob {\n  return !!(value as Blob).arrayBuffer;\n}\n\nfunction isNodeReadableStream(value: any): value is NodeReadableStream {\n  return !!(value as NodeReadableStream).pipe;\n}\n\nexport function responseIterator<T>(\n  response: Response | NodeResponse\n): AsyncIterableIterator<T> {\n  let body: unknown = response;\n\n  if (isNodeResponse(response)) body = response.body;\n\n  if (isAsyncIterableIterator(body)) return asyncIterator<T>(body);\n\n  if (isReadableStream(body)) return readerIterator<T>(body.getReader());\n\n  // this errors without casting to ReadableStream<T>\n  // because Blob.stream() returns a NodeJS ReadableStream\n  if (isStreamableBlob(body)) {\n    return readerIterator<T>(\n      (body.stream() as unknown as ReadableStream<T>).getReader()\n    );\n  }\n\n  if (isBlob(body)) return promiseIterator<T>(body.arrayBuffer());\n\n  if (isNodeReadableStream(body)) return nodeStreamIterator<T>(body);\n\n  throw new Error(\n    \"Unknown body type for responseIterator. Please pass a streamable response.\"\n  );\n}\n"], "mappings": "AAAA;;;;AAOA,SAASA,yBAAyB,QAAQ,0BAA0B;AAEpE,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,eAAe,MAAM,wBAAwB;AACpD,OAAOC,cAAc,MAAM,uBAAuB;AAElD,SAASC,cAAcA,CAACC,KAAU;EAChC,OAAO,CAAC,CAAEA,KAAsB,CAACC,IAAI;AACvC;AAEA,SAASC,gBAAgBA,CAACF,KAAU;EAClC,OAAO,CAAC,CAAEA,KAA6B,CAACG,SAAS;AACnD;AAEA,SAASC,uBAAuBA,CAC9BJ,KAAU;EAEV,OAAO,CAAC,EACNN,yBAAyB,IACxBM,KAAoC,CAACK,MAAM,CAACV,aAAa,CAAC,CAC5D;AACH;AAEA,SAASW,gBAAgBA,CAACN,KAAU;EAClC,OAAO,CAAC,CAAEA,KAAc,CAACO,MAAM;AACjC;AAEA,SAASC,MAAMA,CAACR,KAAU;EACxB,OAAO,CAAC,CAAEA,KAAc,CAACS,WAAW;AACtC;AAEA,SAASC,oBAAoBA,CAACV,KAAU;EACtC,OAAO,CAAC,CAAEA,KAA4B,CAACW,IAAI;AAC7C;AAEA,OAAM,SAAUC,gBAAgBA,CAC9BC,QAAiC;EAEjC,IAAIZ,IAAI,GAAYY,QAAQ;EAE5B,IAAId,cAAc,CAACc,QAAQ,CAAC,EAAEZ,IAAI,GAAGY,QAAQ,CAACZ,IAAI;EAElD,IAAIG,uBAAuB,CAACH,IAAI,CAAC,EAAE,OAAON,aAAa,CAAIM,IAAI,CAAC;EAEhE,IAAIC,gBAAgB,CAACD,IAAI,CAAC,EAAE,OAAOH,cAAc,CAAIG,IAAI,CAACE,SAAS,EAAE,CAAC;EAEtE;EACA;EACA,IAAIG,gBAAgB,CAACL,IAAI,CAAC,EAAE;IAC1B,OAAOH,cAAc,CAClBG,IAAI,CAACM,MAAM,EAAmC,CAACJ,SAAS,EAAE,CAC5D;EACH;EAEA,IAAIK,MAAM,CAACP,IAAI,CAAC,EAAE,OAAOJ,eAAe,CAAII,IAAI,CAACQ,WAAW,EAAE,CAAC;EAE/D,IAAIC,oBAAoB,CAACT,IAAI,CAAC,EAAE,OAAOL,kBAAkB,CAAIK,IAAI,CAAC;EAElE,MAAM,IAAIa,KAAK,CACb,4EAA4E,CAC7E;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}