{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives } from \"../../utilities/index.js\";\nimport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport { selectURI } from \"./selectURI.js\";\nimport { handleError, readMultipartBody, parseAndCheckHttpResponse } from \"./parseAndCheckHttpResponse.js\";\nimport { checkFetcher } from \"./checkFetcher.js\";\nimport { selectHttpOptionsAndBodyInternal, defaultPrinter, fallbackHttpConfig } from \"./selectHttpOptionsAndBody.js\";\nimport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\nimport { fromError, filterOperationVariables } from \"../utils/index.js\";\nimport { maybe, getMainDefinition, removeClientSetsFromDocument } from \"../../utilities/index.js\";\nvar backupFetch = maybe(function () {\n  return fetch;\n});\nexport var createHttpLink = function (linkOptions) {\n  if (linkOptions === void 0) {\n    linkOptions = {};\n  }\n  var _a = linkOptions.uri,\n    uri = _a === void 0 ? \"/graphql\" : _a,\n    // use default global fetch if nothing passed in\n    preferredFetch = linkOptions.fetch,\n    _b = linkOptions.print,\n    print = _b === void 0 ? defaultPrinter : _b,\n    includeExtensions = linkOptions.includeExtensions,\n    preserveHeaderCase = linkOptions.preserveHeaderCase,\n    useGETForQueries = linkOptions.useGETForQueries,\n    _c = linkOptions.includeUnusedVariables,\n    includeUnusedVariables = _c === void 0 ? false : _c,\n    requestOptions = __rest(linkOptions, [\"uri\", \"fetch\", \"print\", \"includeExtensions\", \"preserveHeaderCase\", \"useGETForQueries\", \"includeUnusedVariables\"]);\n  if (globalThis.__DEV__ !== false) {\n    // Make sure at least one of preferredFetch, window.fetch, or backupFetch is\n    // defined, so requests won't fail at runtime.\n    checkFetcher(preferredFetch || backupFetch);\n  }\n  var linkConfig = {\n    http: {\n      includeExtensions: includeExtensions,\n      preserveHeaderCase: preserveHeaderCase\n    },\n    options: requestOptions.fetchOptions,\n    credentials: requestOptions.credentials,\n    headers: requestOptions.headers\n  };\n  return new ApolloLink(function (operation) {\n    var chosenURI = selectURI(operation, uri);\n    var context = operation.getContext();\n    // `apollographql-client-*` headers are automatically set if a\n    // `clientAwareness` object is found in the context. These headers are\n    // set first, followed by the rest of the headers pulled from\n    // `context.headers`. If desired, `apollographql-client-*` headers set by\n    // the `clientAwareness` object can be overridden by\n    // `apollographql-client-*` headers set in `context.headers`.\n    var clientAwarenessHeaders = {};\n    if (context.clientAwareness) {\n      var _a = context.clientAwareness,\n        name_1 = _a.name,\n        version = _a.version;\n      if (name_1) {\n        clientAwarenessHeaders[\"apollographql-client-name\"] = name_1;\n      }\n      if (version) {\n        clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n      }\n    }\n    var contextHeaders = __assign(__assign({}, clientAwarenessHeaders), context.headers);\n    var contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: contextHeaders\n    };\n    if (hasDirectives([\"client\"], operation.query)) {\n      var transformedQuery = removeClientSetsFromDocument(operation.query);\n      if (!transformedQuery) {\n        return fromError(new Error(\"HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`.\"));\n      }\n      operation.query = transformedQuery;\n    }\n    //uses fallback, link, and then context to build options\n    var _b = selectHttpOptionsAndBodyInternal(operation, print, fallbackHttpConfig, linkConfig, contextConfig),\n      options = _b.options,\n      body = _b.body;\n    if (body.variables && !includeUnusedVariables) {\n      body.variables = filterOperationVariables(body.variables, operation.query);\n    }\n    var controller;\n    if (!options.signal && typeof AbortController !== \"undefined\") {\n      controller = new AbortController();\n      options.signal = controller.signal;\n    }\n    // If requested, set method to GET if there are no mutations.\n    var definitionIsMutation = function (d) {\n      return d.kind === \"OperationDefinition\" && d.operation === \"mutation\";\n    };\n    var definitionIsSubscription = function (d) {\n      return d.kind === \"OperationDefinition\" && d.operation === \"subscription\";\n    };\n    var isSubscription = definitionIsSubscription(getMainDefinition(operation.query));\n    // does not match custom directives beginning with @defer\n    var hasDefer = hasDirectives([\"defer\"], operation.query);\n    if (useGETForQueries && !operation.query.definitions.some(definitionIsMutation)) {\n      options.method = \"GET\";\n    }\n    if (hasDefer || isSubscription) {\n      options.headers = options.headers || {};\n      var acceptHeader = \"multipart/mixed;\";\n      // Omit defer-specific headers if the user attempts to defer a selection\n      // set on a subscription and log a warning.\n      if (isSubscription && hasDefer) {\n        globalThis.__DEV__ !== false && invariant.warn(41);\n      }\n      if (isSubscription) {\n        acceptHeader += \"boundary=graphql;subscriptionSpec=1.0,application/json\";\n      } else if (hasDefer) {\n        acceptHeader += \"deferSpec=20220824,application/json\";\n      }\n      options.headers.accept = acceptHeader;\n    }\n    if (options.method === \"GET\") {\n      var _c = rewriteURIForGET(chosenURI, body),\n        newURI = _c.newURI,\n        parseError = _c.parseError;\n      if (parseError) {\n        return fromError(parseError);\n      }\n      chosenURI = newURI;\n    } else {\n      try {\n        options.body = serializeFetchParameter(body, \"Payload\");\n      } catch (parseError) {\n        return fromError(parseError);\n      }\n    }\n    return new Observable(function (observer) {\n      // Prefer linkOptions.fetch (preferredFetch) if provided, and otherwise\n      // fall back to the *current* global window.fetch function (see issue\n      // #7832), or (if all else fails) the backupFetch function we saved when\n      // this module was first evaluated. This last option protects against the\n      // removal of window.fetch, which is unlikely but not impossible.\n      var currentFetch = preferredFetch || maybe(function () {\n        return fetch;\n      }) || backupFetch;\n      var observerNext = observer.next.bind(observer);\n      currentFetch(chosenURI, options).then(function (response) {\n        var _a;\n        operation.setContext({\n          response: response\n        });\n        var ctype = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.get(\"content-type\");\n        if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n          return readMultipartBody(response, observerNext);\n        } else {\n          return parseAndCheckHttpResponse(operation)(response).then(observerNext);\n        }\n      }).then(function () {\n        controller = undefined;\n        observer.complete();\n      }).catch(function (err) {\n        controller = undefined;\n        handleError(err, observer);\n      });\n      return function () {\n        // XXX support canceling this request\n        // https://developers.google.com/web/updates/2017/09/abortable-fetch\n        if (controller) controller.abort();\n      };\n    });\n  });\n};", "map": {"version": 3, "names": ["invariant", "ApolloLink", "Observable", "hasDirectives", "serializeFetchParameter", "selectURI", "handleError", "readMultipartBody", "parseAndCheckHttpResponse", "checkFetcher", "selectHttpOptionsAndBodyInternal", "defaultPrinter", "fallbackHttpConfig", "rewriteURIForGET", "fromError", "filterOperationVariables", "maybe", "getMainDefinition", "removeClientSetsFromDocument", "<PERSON><PERSON><PERSON><PERSON>", "fetch", "createHttpLink", "linkOptions", "_a", "uri", "preferredFetch", "_b", "print", "includeExtensions", "preserveHeaderCase", "useGETForQueries", "_c", "includeUnusedVariables", "requestOptions", "__rest", "globalThis", "__DEV__", "linkConfig", "http", "options", "fetchOptions", "credentials", "headers", "operation", "chosenURI", "context", "getContext", "clientAwarenessHeaders", "clientAwareness", "name_1", "name", "version", "contextHeaders", "__assign", "contextConfig", "query", "<PERSON><PERSON><PERSON><PERSON>", "Error", "body", "variables", "controller", "signal", "AbortController", "definitionIsMutation", "d", "kind", "definitionIsSubscription", "isSubscription", "<PERSON><PERSON><PERSON><PERSON>", "definitions", "some", "method", "acceptHeader", "warn", "accept", "newURI", "parseError", "observer", "currentFetch", "observerNext", "next", "bind", "then", "response", "setContext", "ctype", "get", "test", "undefined", "complete", "catch", "err", "abort"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/link/http/createHttpLink.ts"], "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\n\nimport type { DefinitionNode } from \"graphql\";\n\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives } from \"../../utilities/index.js\";\nimport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport { selectURI } from \"./selectURI.js\";\nimport {\n  handleError,\n  readMultipartBody,\n  parseAndCheckHttpResponse,\n} from \"./parseAndCheckHttpResponse.js\";\nimport { checkFetcher } from \"./checkFetcher.js\";\nimport type { HttpOptions } from \"./selectHttpOptionsAndBody.js\";\nimport {\n  selectHttpOptionsAndBodyInternal,\n  defaultPrinter,\n  fallbackHttpConfig,\n} from \"./selectHttpOptionsAndBody.js\";\nimport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\nimport { fromError, filterOperationVariables } from \"../utils/index.js\";\nimport {\n  maybe,\n  getMainDefinition,\n  removeClientSetsFromDocument,\n} from \"../../utilities/index.js\";\n\nconst backupFetch = maybe(() => fetch);\n\nexport const createHttpLink = (linkOptions: HttpOptions = {}) => {\n  let {\n    uri = \"/graphql\",\n    // use default global fetch if nothing passed in\n    fetch: preferredFetch,\n    print = defaultPrinter,\n    includeExtensions,\n    preserveHeaderCase,\n    useGETForQueries,\n    includeUnusedVariables = false,\n    ...requestOptions\n  } = linkOptions;\n\n  if (__DEV__) {\n    // Make sure at least one of preferredFetch, window.fetch, or backupFetch is\n    // defined, so requests won't fail at runtime.\n    checkFetcher(preferredFetch || backupFetch);\n  }\n\n  const linkConfig = {\n    http: { includeExtensions, preserveHeaderCase },\n    options: requestOptions.fetchOptions,\n    credentials: requestOptions.credentials,\n    headers: requestOptions.headers,\n  };\n\n  return new ApolloLink((operation) => {\n    let chosenURI = selectURI(operation, uri);\n\n    const context = operation.getContext();\n\n    // `apollographql-client-*` headers are automatically set if a\n    // `clientAwareness` object is found in the context. These headers are\n    // set first, followed by the rest of the headers pulled from\n    // `context.headers`. If desired, `apollographql-client-*` headers set by\n    // the `clientAwareness` object can be overridden by\n    // `apollographql-client-*` headers set in `context.headers`.\n    const clientAwarenessHeaders: {\n      \"apollographql-client-name\"?: string;\n      \"apollographql-client-version\"?: string;\n    } = {};\n\n    if (context.clientAwareness) {\n      const { name, version } = context.clientAwareness;\n      if (name) {\n        clientAwarenessHeaders[\"apollographql-client-name\"] = name;\n      }\n      if (version) {\n        clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n      }\n    }\n\n    const contextHeaders = { ...clientAwarenessHeaders, ...context.headers };\n\n    const contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: contextHeaders,\n    };\n\n    if (hasDirectives([\"client\"], operation.query)) {\n      const transformedQuery = removeClientSetsFromDocument(operation.query);\n\n      if (!transformedQuery) {\n        return fromError(\n          new Error(\n            \"HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`.\"\n          )\n        );\n      }\n\n      operation.query = transformedQuery;\n    }\n\n    //uses fallback, link, and then context to build options\n    const { options, body } = selectHttpOptionsAndBodyInternal(\n      operation,\n      print,\n      fallbackHttpConfig,\n      linkConfig,\n      contextConfig\n    );\n\n    if (body.variables && !includeUnusedVariables) {\n      body.variables = filterOperationVariables(\n        body.variables,\n        operation.query\n      );\n    }\n\n    let controller: AbortController | undefined;\n    if (!options.signal && typeof AbortController !== \"undefined\") {\n      controller = new AbortController();\n      options.signal = controller.signal;\n    }\n\n    // If requested, set method to GET if there are no mutations.\n    const definitionIsMutation = (d: DefinitionNode) => {\n      return d.kind === \"OperationDefinition\" && d.operation === \"mutation\";\n    };\n    const definitionIsSubscription = (d: DefinitionNode) => {\n      return d.kind === \"OperationDefinition\" && d.operation === \"subscription\";\n    };\n    const isSubscription = definitionIsSubscription(\n      getMainDefinition(operation.query)\n    );\n    // does not match custom directives beginning with @defer\n    const hasDefer = hasDirectives([\"defer\"], operation.query);\n    if (\n      useGETForQueries &&\n      !operation.query.definitions.some(definitionIsMutation)\n    ) {\n      options.method = \"GET\";\n    }\n\n    if (hasDefer || isSubscription) {\n      options.headers = options.headers || {};\n      let acceptHeader = \"multipart/mixed;\";\n      // Omit defer-specific headers if the user attempts to defer a selection\n      // set on a subscription and log a warning.\n      if (isSubscription && hasDefer) {\n        invariant.warn(\"Multipart-subscriptions do not support @defer\");\n      }\n\n      if (isSubscription) {\n        acceptHeader +=\n          \"boundary=graphql;subscriptionSpec=1.0,application/json\";\n      } else if (hasDefer) {\n        acceptHeader += \"deferSpec=20220824,application/json\";\n      }\n      options.headers.accept = acceptHeader;\n    }\n\n    if (options.method === \"GET\") {\n      const { newURI, parseError } = rewriteURIForGET(chosenURI, body);\n      if (parseError) {\n        return fromError(parseError);\n      }\n      chosenURI = newURI;\n    } else {\n      try {\n        (options as any).body = serializeFetchParameter(body, \"Payload\");\n      } catch (parseError) {\n        return fromError(parseError);\n      }\n    }\n\n    return new Observable((observer) => {\n      // Prefer linkOptions.fetch (preferredFetch) if provided, and otherwise\n      // fall back to the *current* global window.fetch function (see issue\n      // #7832), or (if all else fails) the backupFetch function we saved when\n      // this module was first evaluated. This last option protects against the\n      // removal of window.fetch, which is unlikely but not impossible.\n      const currentFetch = preferredFetch || maybe(() => fetch) || backupFetch;\n\n      const observerNext = observer.next.bind(observer);\n      currentFetch!(chosenURI, options)\n        .then((response) => {\n          operation.setContext({ response });\n          const ctype = response.headers?.get(\"content-type\");\n\n          if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n            return readMultipartBody(response, observerNext);\n          } else {\n            return parseAndCheckHttpResponse(operation)(response).then(\n              observerNext\n            );\n          }\n        })\n        .then(() => {\n          controller = undefined;\n          observer.complete();\n        })\n        .catch((err) => {\n          controller = undefined;\n          handleError(err, observer);\n        });\n\n      return () => {\n        // XXX support canceling this request\n        // https://developers.google.com/web/updates/2017/09/abortable-fetch\n        if (controller) controller.abort();\n      };\n    });\n  });\n};\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,kCAAkC;AAI5D,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,UAAU,EAAEC,aAAa,QAAQ,0BAA0B;AACpE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SACEC,WAAW,EACXC,iBAAiB,EACjBC,yBAAyB,QACpB,gCAAgC;AACvC,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,SACEC,gCAAgC,EAChCC,cAAc,EACdC,kBAAkB,QACb,+BAA+B;AACtC,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,SAAS,EAAEC,wBAAwB,QAAQ,mBAAmB;AACvE,SACEC,KAAK,EACLC,iBAAiB,EACjBC,4BAA4B,QACvB,0BAA0B;AAEjC,IAAMC,WAAW,GAAGH,KAAK,CAAC;EAAM,OAAAI,KAAK;AAAL,CAAK,CAAC;AAEtC,OAAO,IAAMC,cAAc,GAAG,SAAAA,CAACC,WAA6B;EAA7B,IAAAA,WAAA;IAAAA,WAAA,KAA6B;EAAA;EAExD,IAAAC,EAAA,GASED,WAAW,CAAAE,GATG;IAAhBA,GAAG,GAAAD,EAAA,cAAG,UAAU,GAAAA,EAAA;IAChB;IACOE,cAAc,GAOnBH,WAAW,CAAAF,KAPQ;IACrBM,EAAA,GAMEJ,WAAW,CAAAK,KANS;IAAtBA,KAAK,GAAAD,EAAA,cAAGf,cAAc,GAAAe,EAAA;IACtBE,iBAAiB,GAKfN,WAAW,CAAAM,iBALI;IACjBC,kBAAkB,GAIhBP,WAAW,CAAAO,kBAJK;IAClBC,gBAAgB,GAGdR,WAAW,CAAAQ,gBAHG;IAChBC,EAAA,GAEET,WAAW,CAAAU,sBAFiB;IAA9BA,sBAAsB,GAAAD,EAAA,cAAG,KAAK,GAAAA,EAAA;IAC3BE,cAAc,GAAAC,MAAA,CACfZ,WAAW,EAVX,kHAUH,CADkB;EAGnB,IAAIa,UAAU,CAAAC,OAAA;IACZ;IACA;IACA3B,YAAY,CAACgB,cAAc,IAAIN,WAAW,CAAC;EAC7C;EAEA,IAAMkB,UAAU,GAAG;IACjBC,IAAI,EAAE;MAAEV,iBAAiB,EAAAA,iBAAA;MAAEC,kBAAkB,EAAAA;IAAA,CAAE;IAC/CU,OAAO,EAAEN,cAAc,CAACO,YAAY;IACpCC,WAAW,EAAER,cAAc,CAACQ,WAAW;IACvCC,OAAO,EAAET,cAAc,CAACS;GACzB;EAED,OAAO,IAAIzC,UAAU,CAAC,UAAC0C,SAAS;IAC9B,IAAIC,SAAS,GAAGvC,SAAS,CAACsC,SAAS,EAAEnB,GAAG,CAAC;IAEzC,IAAMqB,OAAO,GAAGF,SAAS,CAACG,UAAU,EAAE;IAEtC;IACA;IACA;IACA;IACA;IACA;IACA,IAAMC,sBAAsB,GAGxB,EAAE;IAEN,IAAIF,OAAO,CAACG,eAAe,EAAE;MACrB,IAAAzB,EAAA,GAAoBsB,OAAO,CAACG,eAAe;QAAzCC,MAAI,GAAA1B,EAAA,CAAA2B,IAAA;QAAEC,OAAO,GAAA5B,EAAA,CAAA4B,OAA4B;MACjD,IAAIF,MAAI,EAAE;QACRF,sBAAsB,CAAC,2BAA2B,CAAC,GAAGE,MAAI;MAC5D;MACA,IAAIE,OAAO,EAAE;QACXJ,sBAAsB,CAAC,8BAA8B,CAAC,GAAGI,OAAO;MAClE;IACF;IAEA,IAAMC,cAAc,GAAAC,QAAA,CAAAA,QAAA,KAAQN,sBAAsB,GAAKF,OAAO,CAACH,OAAO,CAAE;IAExE,IAAMY,aAAa,GAAG;MACpBhB,IAAI,EAAEO,OAAO,CAACP,IAAI;MAClBC,OAAO,EAAEM,OAAO,CAACL,YAAY;MAC7BC,WAAW,EAAEI,OAAO,CAACJ,WAAW;MAChCC,OAAO,EAAEU;KACV;IAED,IAAIjD,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAEwC,SAAS,CAACY,KAAK,CAAC,EAAE;MAC9C,IAAMC,gBAAgB,GAAGtC,4BAA4B,CAACyB,SAAS,CAACY,KAAK,CAAC;MAEtE,IAAI,CAACC,gBAAgB,EAAE;QACrB,OAAO1C,SAAS,CACd,IAAI2C,KAAK,CACP,uMAAuM,CACxM,CACF;MACH;MAEAd,SAAS,CAACY,KAAK,GAAGC,gBAAgB;IACpC;IAEA;IACM,IAAA9B,EAAA,GAAoBhB,gCAAgC,CACxDiC,SAAS,EACThB,KAAK,EACLf,kBAAkB,EAClByB,UAAU,EACViB,aAAa,CACd;MANOf,OAAO,GAAAb,EAAA,CAAAa,OAAA;MAAEmB,IAAI,GAAAhC,EAAA,CAAAgC,IAMpB;IAED,IAAIA,IAAI,CAACC,SAAS,IAAI,CAAC3B,sBAAsB,EAAE;MAC7C0B,IAAI,CAACC,SAAS,GAAG5C,wBAAwB,CACvC2C,IAAI,CAACC,SAAS,EACdhB,SAAS,CAACY,KAAK,CAChB;IACH;IAEA,IAAIK,UAAuC;IAC3C,IAAI,CAACrB,OAAO,CAACsB,MAAM,IAAI,OAAOC,eAAe,KAAK,WAAW,EAAE;MAC7DF,UAAU,GAAG,IAAIE,eAAe,EAAE;MAClCvB,OAAO,CAACsB,MAAM,GAAGD,UAAU,CAACC,MAAM;IACpC;IAEA;IACA,IAAME,oBAAoB,GAAG,SAAAA,CAACC,CAAiB;MAC7C,OAAOA,CAAC,CAACC,IAAI,KAAK,qBAAqB,IAAID,CAAC,CAACrB,SAAS,KAAK,UAAU;IACvE,CAAC;IACD,IAAMuB,wBAAwB,GAAG,SAAAA,CAACF,CAAiB;MACjD,OAAOA,CAAC,CAACC,IAAI,KAAK,qBAAqB,IAAID,CAAC,CAACrB,SAAS,KAAK,cAAc;IAC3E,CAAC;IACD,IAAMwB,cAAc,GAAGD,wBAAwB,CAC7CjD,iBAAiB,CAAC0B,SAAS,CAACY,KAAK,CAAC,CACnC;IACD;IACA,IAAMa,QAAQ,GAAGjE,aAAa,CAAC,CAAC,OAAO,CAAC,EAAEwC,SAAS,CAACY,KAAK,CAAC;IAC1D,IACEzB,gBAAgB,IAChB,CAACa,SAAS,CAACY,KAAK,CAACc,WAAW,CAACC,IAAI,CAACP,oBAAoB,CAAC,EACvD;MACAxB,OAAO,CAACgC,MAAM,GAAG,KAAK;IACxB;IAEA,IAAIH,QAAQ,IAAID,cAAc,EAAE;MAC9B5B,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,EAAE;MACvC,IAAI8B,YAAY,GAAG,kBAAkB;MACrC;MACA;MACA,IAAIL,cAAc,IAAIC,QAAQ,EAAE;QAC9BjC,UAAU,CAAAC,OAAK,cAAApC,SAAA,CAAAyE,IAAA;MACjB;MAEA,IAAIN,cAAc,EAAE;QAClBK,YAAY,IACV,wDAAwD;MAC5D,CAAC,MAAM,IAAIJ,QAAQ,EAAE;QACnBI,YAAY,IAAI,qCAAqC;MACvD;MACAjC,OAAO,CAACG,OAAO,CAACgC,MAAM,GAAGF,YAAY;IACvC;IAEA,IAAIjC,OAAO,CAACgC,MAAM,KAAK,KAAK,EAAE;MACtB,IAAAxC,EAAA,GAAyBlB,gBAAgB,CAAC+B,SAAS,EAAEc,IAAI,CAAC;QAAxDiB,MAAM,GAAA5C,EAAA,CAAA4C,MAAA;QAAEC,UAAU,GAAA7C,EAAA,CAAA6C,UAAsC;MAChE,IAAIA,UAAU,EAAE;QACd,OAAO9D,SAAS,CAAC8D,UAAU,CAAC;MAC9B;MACAhC,SAAS,GAAG+B,MAAM;IACpB,CAAC,MAAM;MACL,IAAI;QACDpC,OAAe,CAACmB,IAAI,GAAGtD,uBAAuB,CAACsD,IAAI,EAAE,SAAS,CAAC;MAClE,CAAC,CAAC,OAAOkB,UAAU,EAAE;QACnB,OAAO9D,SAAS,CAAC8D,UAAU,CAAC;MAC9B;IACF;IAEA,OAAO,IAAI1E,UAAU,CAAC,UAAC2E,QAAQ;MAC7B;MACA;MACA;MACA;MACA;MACA,IAAMC,YAAY,GAAGrD,cAAc,IAAIT,KAAK,CAAC;QAAM,OAAAI,KAAK;MAAL,CAAK,CAAC,IAAID,WAAW;MAExE,IAAM4D,YAAY,GAAGF,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC;MACjDC,YAAa,CAAClC,SAAS,EAAEL,OAAO,CAAC,CAC9B2C,IAAI,CAAC,UAACC,QAAQ;;QACbxC,SAAS,CAACyC,UAAU,CAAC;UAAED,QAAQ,EAAAA;QAAA,CAAE,CAAC;QAClC,IAAME,KAAK,GAAG,CAAA9D,EAAA,GAAA4D,QAAQ,CAACzC,OAAO,cAAAnB,EAAA,uBAAAA,EAAA,CAAE+D,GAAG,CAAC,cAAc,CAAC;QAEnD,IAAID,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAACE,IAAI,CAACF,KAAK,CAAC,EAAE;UACtD,OAAO9E,iBAAiB,CAAC4E,QAAQ,EAAEJ,YAAY,CAAC;QAClD,CAAC,MAAM;UACL,OAAOvE,yBAAyB,CAACmC,SAAS,CAAC,CAACwC,QAAQ,CAAC,CAACD,IAAI,CACxDH,YAAY,CACb;QACH;MACF,CAAC,CAAC,CACDG,IAAI,CAAC;QACJtB,UAAU,GAAG4B,SAAS;QACtBX,QAAQ,CAACY,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDC,KAAK,CAAC,UAACC,GAAG;QACT/B,UAAU,GAAG4B,SAAS;QACtBlF,WAAW,CAACqF,GAAG,EAAEd,QAAQ,CAAC;MAC5B,CAAC,CAAC;MAEJ,OAAO;QACL;QACA;QACA,IAAIjB,UAAU,EAAEA,UAAU,CAACgC,KAAK,EAAE;MACpC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}