{"ast": null, "code": "/**\n * Returns true if the provided object is an Object (i.e. not a string literal)\n * and implements the Iterator protocol.\n *\n * This may be used in place of [Array.isArray()][isArray] to determine if\n * an object should be iterated-over e.g. Array, Map, Set, Int8Array,\n * TypedArray, etc. but excludes string literals.\n *\n * @example\n * ```ts\n * isIterableObject([ 1, 2, 3 ]) // true\n * isIterableObject(new Map()) // true\n * isIterableObject('ABC') // false\n * isIterableObject({ key: 'value' }) // false\n * isIterableObject({ length: 1, 0: 'Alpha' }) // false\n * ```\n */\nexport function isIterableObject(maybeIterable) {\n  return typeof maybeIterable === 'object' && typeof (maybeIterable === null || maybeIterable === void 0 ? void 0 : maybeIterable[Symbol.iterator]) === 'function';\n}", "map": {"version": 3, "names": ["isIterableObject", "maybeIterable", "Symbol", "iterator"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/graphql/jsutils/isIterableObject.mjs"], "sourcesContent": ["/**\n * Returns true if the provided object is an Object (i.e. not a string literal)\n * and implements the Iterator protocol.\n *\n * This may be used in place of [Array.isArray()][isArray] to determine if\n * an object should be iterated-over e.g. Array, Map, Set, Int8Array,\n * TypedArray, etc. but excludes string literals.\n *\n * @example\n * ```ts\n * isIterableObject([ 1, 2, 3 ]) // true\n * isIterableObject(new Map()) // true\n * isIterableObject('ABC') // false\n * isIterableObject({ key: 'value' }) // false\n * isIterableObject({ length: 1, 0: 'Alpha' }) // false\n * ```\n */\nexport function isIterableObject(maybeIterable) {\n  return (\n    typeof maybeIterable === 'object' &&\n    typeof (maybeIterable === null || maybeIterable === void 0\n      ? void 0\n      : maybeIterable[Symbol.iterator]) === 'function'\n  );\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAACC,aAAa,EAAE;EAC9C,OACE,OAAOA,aAAa,KAAK,QAAQ,IACjC,QAAQA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GACtD,KAAK,CAAC,GACNA,aAAa,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,KAAK,UAAU;AAEtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}