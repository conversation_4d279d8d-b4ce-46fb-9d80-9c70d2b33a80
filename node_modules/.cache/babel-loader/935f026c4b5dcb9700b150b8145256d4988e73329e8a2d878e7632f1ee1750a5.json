{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\n/**\n * Function parameters in this file try to follow a common order for the sake of\n * readability and consistency. The order is as follows:\n *\n * resultData\n * observable\n * client\n * query\n * options\n * watchQueryOptions\n * makeWatchQueryOptions\n * isSSRAllowed\n * disableNetworkFetches\n * partialRefetch\n * renderPromises\n * isSyncSSR\n * callbacks\n */\n/** */\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { equal } from \"@wry/equality\";\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport { getApolloContext } from \"../context/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport { ObservableQuery, NetworkStatus } from \"../../core/index.js\";\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { compact, isNonEmptyArray, maybeDeepFreeze } from \"../../utilities/index.js\";\nimport { wrapHook } from \"./internal/index.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction noop() {}\nvar lastWatchOptions = Symbol();\n/**\n * A hook for executing queries in an Apollo application.\n *\n * To run a query within a React component, call `useQuery` and pass it a GraphQL query document.\n *\n * When your component renders, `useQuery` returns an object from Apollo Client that contains `loading`, `error`, and `data` properties you can use to render your UI.\n *\n * > Refer to the [Queries](https://www.apollographql.com/docs/react/data/queries) section for a more in-depth overview of `useQuery`.\n *\n * @example\n * ```jsx\n * import { gql, useQuery } from '@apollo/client';\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const { loading, error, data } = useQuery(GET_GREETING, {\n *     variables: { language: 'english' },\n *   });\n *   if (loading) return <p>Loading ...</p>;\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n * @since 3.0.0\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns Query result object\n */\nexport function useQuery(query, options) {\n  if (options === void 0) {\n    options = Object.create(null);\n  }\n  return wrapHook(\"useQuery\",\n  // eslint-disable-next-line react-compiler/react-compiler\n  useQuery_, useApolloClient(options && options.client))(query, options);\n}\nfunction useQuery_(query, options) {\n  var _a = useQueryInternals(query, options),\n    result = _a.result,\n    obsQueryFields = _a.obsQueryFields;\n  return React.useMemo(function () {\n    return __assign(__assign({}, result), obsQueryFields);\n  }, [result, obsQueryFields]);\n}\nfunction useInternalState(client, query, options, renderPromises, makeWatchQueryOptions) {\n  function createInternalState(previous) {\n    var _a;\n    verifyDocumentType(query, DocumentType.Query);\n    var internalState = {\n      client: client,\n      query: query,\n      observable:\n      // See if there is an existing observable that was used to fetch the same\n      // data and if so, use it instead since it will contain the proper queryId\n      // to fetch the result set. This is used during SSR.\n      renderPromises && renderPromises.getSSRObservable(makeWatchQueryOptions()) || ObservableQuery[\"inactiveOnCreation\"].withValue(!renderPromises, function () {\n        return client.watchQuery(getObsQueryOptions(void 0, client, options, makeWatchQueryOptions()));\n      }),\n      resultData: {\n        // Reuse previousData from previous InternalState (if any) to provide\n        // continuity of previousData even if/when the query or client changes.\n        previousData: (_a = previous === null || previous === void 0 ? void 0 : previous.resultData.current) === null || _a === void 0 ? void 0 : _a.data\n      }\n    };\n    return internalState;\n  }\n  var _a = React.useState(createInternalState),\n    internalState = _a[0],\n    updateInternalState = _a[1];\n  /**\n   * Used by `useLazyQuery` when a new query is executed.\n   * We keep this logic here since it needs to update things in unsafe\n   * ways and here we at least can keep track of that in a single place.\n   */\n  function onQueryExecuted(watchQueryOptions) {\n    var _a;\n    var _b;\n    // this needs to be set to prevent an immediate `resubscribe` in the\n    // next rerender of the `useQuery` internals\n    Object.assign(internalState.observable, (_a = {}, _a[lastWatchOptions] = watchQueryOptions, _a));\n    var resultData = internalState.resultData;\n    updateInternalState(__assign(__assign({}, internalState), {\n      // might be a different query\n      query: watchQueryOptions.query,\n      resultData: Object.assign(resultData, {\n        // We need to modify the previous `resultData` object as we rely on the\n        // object reference in other places\n        previousData: ((_b = resultData.current) === null || _b === void 0 ? void 0 : _b.data) || resultData.previousData,\n        current: undefined\n      })\n    }));\n  }\n  if (client !== internalState.client || query !== internalState.query) {\n    // If the client or query have changed, we need to create a new InternalState.\n    // This will trigger a re-render with the new state, but it will also continue\n    // to run the current render function to completion.\n    // Since we sometimes trigger some side-effects in the render function, we\n    // re-assign `state` to the new state to ensure that those side-effects are\n    // triggered with the new state.\n    var newInternalState = createInternalState(internalState);\n    updateInternalState(newInternalState);\n    return [newInternalState, onQueryExecuted];\n  }\n  return [internalState, onQueryExecuted];\n}\nexport function useQueryInternals(query, options) {\n  var client = useApolloClient(options.client);\n  var renderPromises = React.useContext(getApolloContext()).renderPromises;\n  var isSyncSSR = !!renderPromises;\n  var disableNetworkFetches = client.disableNetworkFetches;\n  var ssrAllowed = options.ssr !== false && !options.skip;\n  var partialRefetch = options.partialRefetch;\n  var makeWatchQueryOptions = createMakeWatchQueryOptions(client, query, options, isSyncSSR);\n  var _a = useInternalState(client, query, options, renderPromises, makeWatchQueryOptions),\n    _b = _a[0],\n    observable = _b.observable,\n    resultData = _b.resultData,\n    onQueryExecuted = _a[1];\n  var watchQueryOptions = makeWatchQueryOptions(observable);\n  useResubscribeIfNecessary(resultData,\n  // might get mutated during render\n  observable,\n  // might get mutated during render\n  client, options, watchQueryOptions);\n  var obsQueryFields = React.useMemo(function () {\n    return bindObservableMethods(observable);\n  }, [observable]);\n  useRegisterSSRObservable(observable, renderPromises, ssrAllowed);\n  var result = useObservableSubscriptionResult(resultData, observable, client, options, watchQueryOptions, disableNetworkFetches, partialRefetch, isSyncSSR, {\n    onCompleted: options.onCompleted || noop,\n    onError: options.onError || noop\n  });\n  return {\n    result: result,\n    obsQueryFields: obsQueryFields,\n    observable: observable,\n    resultData: resultData,\n    client: client,\n    onQueryExecuted: onQueryExecuted\n  };\n}\nfunction useObservableSubscriptionResult(resultData, observable, client, options, watchQueryOptions, disableNetworkFetches, partialRefetch, isSyncSSR, callbacks) {\n  var callbackRef = React.useRef(callbacks);\n  React.useEffect(function () {\n    // Make sure state.onCompleted and state.onError always reflect the latest\n    // options.onCompleted and options.onError callbacks provided to useQuery,\n    // since those functions are often recreated every time useQuery is called.\n    // Like the forceUpdate method, the versions of these methods inherited from\n    // InternalState.prototype are empty no-ops, but we can override them on the\n    // base state object (without modifying the prototype).\n    // eslint-disable-next-line react-compiler/react-compiler\n    callbackRef.current = callbacks;\n  });\n  var resultOverride = (isSyncSSR || disableNetworkFetches) && options.ssr === false && !options.skip ?\n  // If SSR has been explicitly disabled, and this function has been called\n  // on the server side, return the default loading state.\n  ssrDisabledResult : options.skip || watchQueryOptions.fetchPolicy === \"standby\" ?\n  // When skipping a query (ie. we're not querying for data but still want to\n  // render children), make sure the `data` is cleared out and `loading` is\n  // set to `false` (since we aren't loading anything).\n  //\n  // NOTE: We no longer think this is the correct behavior. Skipping should\n  // not automatically set `data` to `undefined`, but instead leave the\n  // previous data in place. In other words, skipping should not mandate that\n  // previously received data is all of a sudden removed. Unfortunately,\n  // changing this is breaking, so we'll have to wait until Apollo Client 4.0\n  // to address this.\n  skipStandbyResult : void 0;\n  var previousData = resultData.previousData;\n  var currentResultOverride = React.useMemo(function () {\n    return resultOverride && toQueryResult(resultOverride, previousData, observable, client);\n  }, [client, observable, resultOverride, previousData]);\n  return useSyncExternalStore(React.useCallback(function (handleStoreChange) {\n    // reference `disableNetworkFetches` here to ensure that the rules of hooks\n    // keep it as a dependency of this effect, even though it's not used\n    disableNetworkFetches;\n    if (isSyncSSR) {\n      return function () {};\n    }\n    var onNext = function () {\n      var previousResult = resultData.current;\n      // We use `getCurrentResult()` instead of the onNext argument because\n      // the values differ slightly. Specifically, loading results will have\n      // an empty object for data instead of `undefined` for some reason.\n      var result = observable.getCurrentResult();\n      // Make sure we're not attempting to re-render similar results\n      if (previousResult && previousResult.loading === result.loading && previousResult.networkStatus === result.networkStatus && equal(previousResult.data, result.data)) {\n        return;\n      }\n      setResult(result, resultData, observable, client, partialRefetch, handleStoreChange, callbackRef.current);\n    };\n    var onError = function (error) {\n      subscription.current.unsubscribe();\n      subscription.current = observable.resubscribeAfterError(onNext, onError);\n      if (!hasOwnProperty.call(error, \"graphQLErrors\")) {\n        // The error is not a GraphQL error\n        throw error;\n      }\n      var previousResult = resultData.current;\n      if (!previousResult || previousResult && previousResult.loading || !equal(error, previousResult.error)) {\n        setResult({\n          data: previousResult && previousResult.data,\n          error: error,\n          loading: false,\n          networkStatus: NetworkStatus.error\n        }, resultData, observable, client, partialRefetch, handleStoreChange, callbackRef.current);\n      }\n    };\n    // TODO evaluate if we keep this in\n    // React Compiler cannot handle scoped `let` access, but a mutable object\n    // like this is fine.\n    // was:\n    // let subscription = observable.subscribe(onNext, onError);\n    var subscription = {\n      current: observable.subscribe(onNext, onError)\n    };\n    // Do the \"unsubscribe\" with a short delay.\n    // This way, an existing subscription can be reused without an additional\n    // request if \"unsubscribe\"  and \"resubscribe\" to the same ObservableQuery\n    // happen in very fast succession.\n    return function () {\n      setTimeout(function () {\n        return subscription.current.unsubscribe();\n      });\n    };\n  }, [disableNetworkFetches, isSyncSSR, observable, resultData, partialRefetch, client]), function () {\n    return currentResultOverride || getCurrentResult(resultData, observable, callbackRef.current, partialRefetch, client);\n  }, function () {\n    return currentResultOverride || getCurrentResult(resultData, observable, callbackRef.current, partialRefetch, client);\n  });\n}\nfunction useRegisterSSRObservable(observable, renderPromises, ssrAllowed) {\n  if (renderPromises && ssrAllowed) {\n    renderPromises.registerSSRObservable(observable);\n    if (observable.getCurrentResult().loading) {\n      // TODO: This is a legacy API which could probably be cleaned up\n      renderPromises.addObservableQueryPromise(observable);\n    }\n  }\n}\n// this hook is not compatible with any rules of React, and there's no good way to rewrite it.\n// it should stay a separate hook that will not be optimized by the compiler\nfunction useResubscribeIfNecessary(/** this hook will mutate properties on `resultData` */\nresultData, /** this hook will mutate properties on `observable` */\nobservable, client, options, watchQueryOptions) {\n  var _a;\n  if (observable[lastWatchOptions] && !equal(observable[lastWatchOptions], watchQueryOptions)) {\n    // Though it might be tempting to postpone this reobserve call to the\n    // useEffect block, we need getCurrentResult to return an appropriate\n    // loading:true result synchronously (later within the same call to\n    // useQuery). Since we already have this.observable here (not true for\n    // the very first call to useQuery), we are not initiating any new\n    // subscriptions, though it does feel less than ideal that reobserve\n    // (potentially) kicks off a network request (for example, when the\n    // variables have changed), which is technically a side-effect.\n    observable.reobserve(getObsQueryOptions(observable, client, options, watchQueryOptions));\n    // Make sure getCurrentResult returns a fresh ApolloQueryResult<TData>,\n    // but save the current data as this.previousData, just like setResult\n    // usually does.\n    resultData.previousData = ((_a = resultData.current) === null || _a === void 0 ? void 0 : _a.data) || resultData.previousData;\n    resultData.current = void 0;\n  }\n  observable[lastWatchOptions] = watchQueryOptions;\n}\n/*\n * A function to massage options before passing them to ObservableQuery.\n * This is two-step curried because we want to reuse the `make` function,\n * but the `observable` might differ between calls to `make`.\n */\nexport function createMakeWatchQueryOptions(client, query, _a, isSyncSSR) {\n  if (_a === void 0) {\n    _a = {};\n  }\n  var skip = _a.skip,\n    ssr = _a.ssr,\n    onCompleted = _a.onCompleted,\n    onError = _a.onError,\n    defaultOptions = _a.defaultOptions,\n    // The above options are useQuery-specific, so this ...otherOptions spread\n    // makes otherOptions almost a WatchQueryOptions object, except for the\n    // query property that we add below.\n    otherOptions = __rest(_a, [\"skip\", \"ssr\", \"onCompleted\", \"onError\", \"defaultOptions\"]);\n  return function (observable) {\n    // This Object.assign is safe because otherOptions is a fresh ...rest object\n    // that did not exist until just now, so modifications are still allowed.\n    var watchQueryOptions = Object.assign(otherOptions, {\n      query: query\n    });\n    if (isSyncSSR && (watchQueryOptions.fetchPolicy === \"network-only\" || watchQueryOptions.fetchPolicy === \"cache-and-network\")) {\n      // this behavior was added to react-apollo without explanation in this PR\n      // https://github.com/apollographql/react-apollo/pull/1579\n      watchQueryOptions.fetchPolicy = \"cache-first\";\n    }\n    if (!watchQueryOptions.variables) {\n      watchQueryOptions.variables = {};\n    }\n    if (skip) {\n      // When skipping, we set watchQueryOptions.fetchPolicy initially to\n      // \"standby\", but we also need/want to preserve the initial non-standby\n      // fetchPolicy that would have been used if not skipping.\n      watchQueryOptions.initialFetchPolicy = watchQueryOptions.initialFetchPolicy || watchQueryOptions.fetchPolicy || getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n      watchQueryOptions.fetchPolicy = \"standby\";\n    } else if (!watchQueryOptions.fetchPolicy) {\n      watchQueryOptions.fetchPolicy = (observable === null || observable === void 0 ? void 0 : observable.options.initialFetchPolicy) || getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n    }\n    return watchQueryOptions;\n  };\n}\nexport function getObsQueryOptions(observable, client, queryHookOptions, watchQueryOptions) {\n  var toMerge = [];\n  var globalDefaults = client.defaultOptions.watchQuery;\n  if (globalDefaults) toMerge.push(globalDefaults);\n  if (queryHookOptions.defaultOptions) {\n    toMerge.push(queryHookOptions.defaultOptions);\n  }\n  // We use compact rather than mergeOptions for this part of the merge,\n  // because we want watchQueryOptions.variables (if defined) to replace\n  // this.observable.options.variables whole. This replacement allows\n  // removing variables by removing them from the variables input to\n  // useQuery. If the variables were always merged together (rather than\n  // replaced), there would be no way to remove existing variables.\n  // However, the variables from options.defaultOptions and globalDefaults\n  // (if provided) should be merged, to ensure individual defaulted\n  // variables always have values, if not otherwise defined in\n  // observable.options or watchQueryOptions.\n  toMerge.push(compact(observable && observable.options, watchQueryOptions));\n  return toMerge.reduce(mergeOptions);\n}\nfunction setResult(nextResult, resultData, observable, client, partialRefetch, forceUpdate, callbacks) {\n  var previousResult = resultData.current;\n  if (previousResult && previousResult.data) {\n    resultData.previousData = previousResult.data;\n  }\n  if (!nextResult.error && isNonEmptyArray(nextResult.errors)) {\n    // Until a set naming convention for networkError and graphQLErrors is\n    // decided upon, we map errors (graphQLErrors) to the error options.\n    // TODO: Is it possible for both result.error and result.errors to be\n    // defined here?\n    nextResult.error = new ApolloError({\n      graphQLErrors: nextResult.errors\n    });\n  }\n  resultData.current = toQueryResult(unsafeHandlePartialRefetch(nextResult, observable, partialRefetch), resultData.previousData, observable, client);\n  // Calling state.setResult always triggers an update, though some call sites\n  // perform additional equality checks before committing to an update.\n  forceUpdate();\n  handleErrorOrCompleted(nextResult, previousResult === null || previousResult === void 0 ? void 0 : previousResult.networkStatus, callbacks);\n}\nfunction handleErrorOrCompleted(result, previousNetworkStatus, callbacks) {\n  if (!result.loading) {\n    var error_1 = toApolloError(result);\n    // wait a tick in case we are in the middle of rendering a component\n    Promise.resolve().then(function () {\n      if (error_1) {\n        callbacks.onError(error_1);\n      } else if (result.data && previousNetworkStatus !== result.networkStatus && result.networkStatus === NetworkStatus.ready) {\n        callbacks.onCompleted(result.data);\n      }\n    }).catch(function (error) {\n      globalThis.__DEV__ !== false && invariant.warn(error);\n    });\n  }\n}\nfunction getCurrentResult(resultData, observable, callbacks, partialRefetch, client) {\n  // Using this.result as a cache ensures getCurrentResult continues returning\n  // the same (===) result object, unless state.setResult has been called, or\n  // we're doing server rendering and therefore override the result below.\n  if (!resultData.current) {\n    // WARNING: SIDE-EFFECTS IN THE RENDER FUNCTION\n    // this could call unsafeHandlePartialRefetch\n    setResult(observable.getCurrentResult(), resultData, observable, client, partialRefetch, function () {}, callbacks);\n  }\n  return resultData.current;\n}\nexport function getDefaultFetchPolicy(queryHookDefaultOptions, clientDefaultOptions) {\n  var _a;\n  return (queryHookDefaultOptions === null || queryHookDefaultOptions === void 0 ? void 0 : queryHookDefaultOptions.fetchPolicy) || ((_a = clientDefaultOptions === null || clientDefaultOptions === void 0 ? void 0 : clientDefaultOptions.watchQuery) === null || _a === void 0 ? void 0 : _a.fetchPolicy) || \"cache-first\";\n}\nexport function toApolloError(result) {\n  return isNonEmptyArray(result.errors) ? new ApolloError({\n    graphQLErrors: result.errors\n  }) : result.error;\n}\nexport function toQueryResult(result, previousData, observable, client) {\n  var data = result.data,\n    partial = result.partial,\n    resultWithoutPartial = __rest(result, [\"data\", \"partial\"]);\n  var queryResult = __assign(__assign({\n    data: data\n  }, resultWithoutPartial), {\n    client: client,\n    observable: observable,\n    variables: observable.variables,\n    called: result !== ssrDisabledResult && result !== skipStandbyResult,\n    previousData: previousData\n  });\n  return queryResult;\n}\nfunction unsafeHandlePartialRefetch(result, observable, partialRefetch) {\n  // TODO: This code should be removed when the partialRefetch option is\n  // removed. I was unable to get this hook to behave reasonably in certain\n  // edge cases when this block was put in an effect.\n  if (result.partial && partialRefetch && !result.loading && (!result.data || Object.keys(result.data).length === 0) && observable.options.fetchPolicy !== \"cache-only\") {\n    observable.refetch();\n    return __assign(__assign({}, result), {\n      loading: true,\n      networkStatus: NetworkStatus.refetch\n    });\n  }\n  return result;\n}\nvar ssrDisabledResult = maybeDeepFreeze({\n  loading: true,\n  data: void 0,\n  error: void 0,\n  networkStatus: NetworkStatus.loading\n});\nvar skipStandbyResult = maybeDeepFreeze({\n  loading: false,\n  data: void 0,\n  error: void 0,\n  networkStatus: NetworkStatus.ready\n});\nfunction bindObservableMethods(observable) {\n  return {\n    refetch: observable.refetch.bind(observable),\n    reobserve: observable.reobserve.bind(observable),\n    fetchMore: observable.fetchMore.bind(observable),\n    updateQuery: observable.updateQuery.bind(observable),\n    startPolling: observable.startPolling.bind(observable),\n    stopPolling: observable.stopPolling.bind(observable),\n    subscribeToMore: observable.subscribeToMore.bind(observable)\n  };\n}", "map": {"version": 3, "names": ["invariant", "React", "useSyncExternalStore", "equal", "mergeOptions", "getApolloContext", "ApolloError", "ObservableQuery", "NetworkStatus", "DocumentType", "verifyDocumentType", "useApolloClient", "compact", "isNonEmptyArray", "maybeDeepFreeze", "wrapHook", "hasOwnProperty", "Object", "prototype", "noop", "lastWatchOptions", "Symbol", "useQuery", "query", "options", "create", "useQuery_", "client", "_a", "useQueryInternals", "result", "obsQuery<PERSON>ields", "useMemo", "__assign", "useInternalState", "renderPromises", "makeWatchQueryOptions", "createInternalState", "previous", "Query", "internalState", "observable", "getSSRObservable", "with<PERSON><PERSON><PERSON>", "watch<PERSON><PERSON>y", "getObsQueryOptions", "resultData", "previousData", "current", "data", "useState", "updateInternalState", "onQueryExecuted", "watchQueryOptions", "assign", "_b", "undefined", "newInternalState", "useContext", "isSyncSSR", "disableNetworkFetches", "ssrAllowed", "ssr", "skip", "partialRefetch", "createMakeWatchQueryOptions", "useResubscribeIfNecessary", "bindObservableMethods", "useRegisterSSRObservable", "useObservableSubscriptionResult", "onCompleted", "onError", "callbacks", "callback<PERSON><PERSON>", "useRef", "useEffect", "resultOverride", "ssrDisabledResult", "fetchPolicy", "skipStandbyResult", "currentResultOverride", "to<PERSON><PERSON><PERSON><PERSON><PERSON>ult", "useCallback", "handleStoreChange", "onNext", "previousResult", "getCurrentResult", "loading", "networkStatus", "setResult", "error", "subscription", "unsubscribe", "resubscribeAfterError", "call", "subscribe", "setTimeout", "registerSSRObservable", "addObservableQueryPromise", "reobserve", "defaultOptions", "otherOptions", "__rest", "variables", "initialFetchPolicy", "getDefaultFetchPolicy", "queryHookOptions", "toMerge", "globalDefaults", "push", "reduce", "nextResult", "forceUpdate", "errors", "graphQLErrors", "unsafeHandlePartialRefetch", "handleErrorOrCompleted", "previousNetworkStatus", "error_1", "toApolloError", "Promise", "resolve", "then", "ready", "catch", "globalThis", "__DEV__", "warn", "queryHookDefaultOptions", "clientDefaultOptions", "partial", "resultWithoutPartial", "query<PERSON><PERSON>ult", "called", "keys", "length", "refetch", "bind", "fetchMore", "updateQuery", "startPolling", "stopPolling", "subscribeToMore"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/react/hooks/useQuery.ts"], "sourcesContent": ["/**\n * Function parameters in this file try to follow a common order for the sake of\n * readability and consistency. The order is as follows:\n *\n * resultData\n * observable\n * client\n * query\n * options\n * watchQueryOptions\n * makeWatchQueryOptions\n * isSSRAllowed\n * disableNetworkFetches\n * partialRefetch\n * renderPromises\n * isSyncSSR\n * callbacks\n */\n/** */\nimport { invariant } from \"../../utilities/globals/index.js\";\n\nimport * as React from \"rehackt\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { equal } from \"@wry/equality\";\n\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport { getApolloContext } from \"../context/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport type {\n  ApolloClient,\n  DefaultOptions,\n  OperationVariables,\n  WatchQueryFetchPolicy,\n  ApolloQueryResult,\n  DocumentNode,\n  TypedDocumentNode,\n  WatchQueryOptions,\n} from \"../../core/index.js\";\nimport { ObservableQuery, NetworkStatus } from \"../../core/index.js\";\nimport type {\n  QueryHookOptions,\n  QueryResult,\n  ObservableQueryFields,\n  NoInfer,\n} from \"../types/types.js\";\n\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport {\n  compact,\n  isNonEmptyArray,\n  maybeDeepFreeze,\n} from \"../../utilities/index.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport type { RenderPromises } from \"../ssr/RenderPromises.js\";\nimport type { MaybeMasked } from \"../../masking/index.js\";\n\nconst {\n  prototype: { hasOwnProperty },\n} = Object;\n\ntype InternalQueryResult<TData, TVariables extends OperationVariables> = Omit<\n  QueryResult<TData, TVariables>,\n  Exclude<keyof ObservableQueryFields<TData, TVariables>, \"variables\">\n>;\n\nfunction noop() {}\nconst lastWatchOptions = Symbol();\n\ninterface ObsQueryWithMeta<TData, TVariables extends OperationVariables>\n  extends ObservableQuery<TData, TVariables> {\n  [lastWatchOptions]?: WatchQueryOptions<TVariables, TData>;\n}\n\nexport interface InternalResult<TData, TVariables extends OperationVariables> {\n  // These members are populated by getCurrentResult and setResult, and it's\n  // okay/normal for them to be initially undefined.\n  current?: undefined | InternalQueryResult<TData, TVariables>;\n  previousData?: undefined | MaybeMasked<TData>;\n}\n\ninterface InternalState<TData, TVariables extends OperationVariables> {\n  client: ReturnType<typeof useApolloClient>;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  observable: ObsQueryWithMeta<TData, TVariables>;\n  resultData: InternalResult<TData, TVariables>;\n}\n\nexport type UpdateInternalState<\n  TData,\n  TVariables extends OperationVariables,\n> = (state: InternalState<TData, TVariables>) => void;\n\ninterface Callbacks<TData> {\n  // Defining these methods as no-ops on the prototype allows us to call\n  // state.onCompleted and/or state.onError without worrying about whether a\n  // callback was provided.\n  onCompleted(data: MaybeMasked<TData>): void;\n  onError(error: ApolloError): void;\n}\n\n/**\n * A hook for executing queries in an Apollo application.\n *\n * To run a query within a React component, call `useQuery` and pass it a GraphQL query document.\n *\n * When your component renders, `useQuery` returns an object from Apollo Client that contains `loading`, `error`, and `data` properties you can use to render your UI.\n *\n * > Refer to the [Queries](https://www.apollographql.com/docs/react/data/queries) section for a more in-depth overview of `useQuery`.\n *\n * @example\n * ```jsx\n * import { gql, useQuery } from '@apollo/client';\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const { loading, error, data } = useQuery(GET_GREETING, {\n *     variables: { language: 'english' },\n *   });\n *   if (loading) return <p>Loading ...</p>;\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n * @since 3.0.0\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns Query result object\n */\nexport function useQuery<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<\n    NoInfer<TData>,\n    NoInfer<TVariables>\n  > = Object.create(null)\n): QueryResult<TData, TVariables> {\n  return wrapHook(\n    \"useQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useQuery_,\n    useApolloClient(options && options.client)\n  )(query, options);\n}\n\nfunction useQuery_<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>\n) {\n  const { result, obsQueryFields } = useQueryInternals(query, options);\n  return React.useMemo(\n    () => ({ ...result, ...obsQueryFields }),\n    [result, obsQueryFields]\n  );\n}\n\nfunction useInternalState<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  client: ApolloClient<object>,\n  query: DocumentNode | TypedDocumentNode<any, any>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>,\n  renderPromises: RenderPromises | undefined,\n  makeWatchQueryOptions: () => WatchQueryOptions<TVariables, TData>\n) {\n  function createInternalState(previous?: InternalState<TData, TVariables>) {\n    verifyDocumentType(query, DocumentType.Query);\n\n    const internalState: InternalState<TData, TVariables> = {\n      client,\n      query,\n      observable:\n        // See if there is an existing observable that was used to fetch the same\n        // data and if so, use it instead since it will contain the proper queryId\n        // to fetch the result set. This is used during SSR.\n        (renderPromises &&\n          renderPromises.getSSRObservable(makeWatchQueryOptions())) ||\n        ObservableQuery[\"inactiveOnCreation\"].withValue(!renderPromises, () =>\n          client.watchQuery(\n            getObsQueryOptions(void 0, client, options, makeWatchQueryOptions())\n          )\n        ),\n      resultData: {\n        // Reuse previousData from previous InternalState (if any) to provide\n        // continuity of previousData even if/when the query or client changes.\n        previousData: previous?.resultData.current?.data,\n      },\n    };\n\n    return internalState as InternalState<TData, TVariables>;\n  }\n\n  let [internalState, updateInternalState] =\n    React.useState(createInternalState);\n\n  /**\n   * Used by `useLazyQuery` when a new query is executed.\n   * We keep this logic here since it needs to update things in unsafe\n   * ways and here we at least can keep track of that in a single place.\n   */\n  function onQueryExecuted(\n    watchQueryOptions: WatchQueryOptions<TVariables, TData>\n  ) {\n    // this needs to be set to prevent an immediate `resubscribe` in the\n    // next rerender of the `useQuery` internals\n    Object.assign(internalState.observable, {\n      [lastWatchOptions]: watchQueryOptions,\n    });\n    const resultData = internalState.resultData;\n    updateInternalState({\n      ...internalState,\n      // might be a different query\n      query: watchQueryOptions.query,\n      resultData: Object.assign(resultData, {\n        // We need to modify the previous `resultData` object as we rely on the\n        // object reference in other places\n        previousData: resultData.current?.data || resultData.previousData,\n        current: undefined,\n      }),\n    });\n  }\n\n  if (client !== internalState.client || query !== internalState.query) {\n    // If the client or query have changed, we need to create a new InternalState.\n    // This will trigger a re-render with the new state, but it will also continue\n    // to run the current render function to completion.\n    // Since we sometimes trigger some side-effects in the render function, we\n    // re-assign `state` to the new state to ensure that those side-effects are\n    // triggered with the new state.\n    const newInternalState = createInternalState(internalState);\n    updateInternalState(newInternalState);\n    return [newInternalState, onQueryExecuted] as const;\n  }\n\n  return [internalState, onQueryExecuted] as const;\n}\n\nexport function useQueryInternals<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>\n) {\n  const client = useApolloClient(options.client);\n\n  const renderPromises = React.useContext(getApolloContext()).renderPromises;\n  const isSyncSSR = !!renderPromises;\n  const disableNetworkFetches = client.disableNetworkFetches;\n  const ssrAllowed = options.ssr !== false && !options.skip;\n  const partialRefetch = options.partialRefetch;\n\n  const makeWatchQueryOptions = createMakeWatchQueryOptions(\n    client,\n    query,\n    options,\n    isSyncSSR\n  );\n\n  const [{ observable, resultData }, onQueryExecuted] = useInternalState(\n    client,\n    query,\n    options,\n    renderPromises,\n    makeWatchQueryOptions\n  );\n\n  const watchQueryOptions: Readonly<WatchQueryOptions<TVariables, TData>> =\n    makeWatchQueryOptions(observable);\n\n  useResubscribeIfNecessary<TData, TVariables>(\n    resultData, // might get mutated during render\n    observable, // might get mutated during render\n    client,\n    options,\n    watchQueryOptions\n  );\n\n  const obsQueryFields = React.useMemo(\n    () => bindObservableMethods(observable),\n    [observable]\n  );\n\n  useRegisterSSRObservable(observable, renderPromises, ssrAllowed);\n\n  const result = useObservableSubscriptionResult<TData, TVariables>(\n    resultData,\n    observable,\n    client,\n    options,\n    watchQueryOptions,\n    disableNetworkFetches,\n    partialRefetch,\n    isSyncSSR,\n    {\n      onCompleted: options.onCompleted || noop,\n      onError: options.onError || noop,\n    }\n  );\n\n  return {\n    result,\n    obsQueryFields,\n    observable,\n    resultData,\n    client,\n    onQueryExecuted,\n  };\n}\n\nfunction useObservableSubscriptionResult<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  resultData: InternalResult<TData, TVariables>,\n  observable: ObservableQuery<TData, TVariables>,\n  client: ApolloClient<object>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>,\n  watchQueryOptions: Readonly<WatchQueryOptions<TVariables, TData>>,\n  disableNetworkFetches: boolean,\n  partialRefetch: boolean | undefined,\n  isSyncSSR: boolean,\n  callbacks: {\n    onCompleted: (data: MaybeMasked<TData>) => void;\n    onError: (error: ApolloError) => void;\n  }\n) {\n  const callbackRef = React.useRef<Callbacks<TData>>(callbacks);\n  React.useEffect(() => {\n    // Make sure state.onCompleted and state.onError always reflect the latest\n    // options.onCompleted and options.onError callbacks provided to useQuery,\n    // since those functions are often recreated every time useQuery is called.\n    // Like the forceUpdate method, the versions of these methods inherited from\n    // InternalState.prototype are empty no-ops, but we can override them on the\n    // base state object (without modifying the prototype).\n    // eslint-disable-next-line react-compiler/react-compiler\n    callbackRef.current = callbacks;\n  });\n\n  const resultOverride =\n    (\n      (isSyncSSR || disableNetworkFetches) &&\n      options.ssr === false &&\n      !options.skip\n    ) ?\n      // If SSR has been explicitly disabled, and this function has been called\n      // on the server side, return the default loading state.\n      ssrDisabledResult\n    : options.skip || watchQueryOptions.fetchPolicy === \"standby\" ?\n      // When skipping a query (ie. we're not querying for data but still want to\n      // render children), make sure the `data` is cleared out and `loading` is\n      // set to `false` (since we aren't loading anything).\n      //\n      // NOTE: We no longer think this is the correct behavior. Skipping should\n      // not automatically set `data` to `undefined`, but instead leave the\n      // previous data in place. In other words, skipping should not mandate that\n      // previously received data is all of a sudden removed. Unfortunately,\n      // changing this is breaking, so we'll have to wait until Apollo Client 4.0\n      // to address this.\n      skipStandbyResult\n    : void 0;\n\n  const previousData = resultData.previousData;\n  const currentResultOverride = React.useMemo(\n    () =>\n      resultOverride &&\n      toQueryResult(resultOverride, previousData, observable, client),\n    [client, observable, resultOverride, previousData]\n  );\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (handleStoreChange) => {\n        // reference `disableNetworkFetches` here to ensure that the rules of hooks\n        // keep it as a dependency of this effect, even though it's not used\n        disableNetworkFetches;\n\n        if (isSyncSSR) {\n          return () => {};\n        }\n\n        const onNext = () => {\n          const previousResult = resultData.current;\n          // We use `getCurrentResult()` instead of the onNext argument because\n          // the values differ slightly. Specifically, loading results will have\n          // an empty object for data instead of `undefined` for some reason.\n          const result = observable.getCurrentResult();\n          // Make sure we're not attempting to re-render similar results\n          if (\n            previousResult &&\n            previousResult.loading === result.loading &&\n            previousResult.networkStatus === result.networkStatus &&\n            equal(previousResult.data, result.data)\n          ) {\n            return;\n          }\n\n          setResult(\n            result,\n            resultData,\n            observable,\n            client,\n            partialRefetch,\n            handleStoreChange,\n            callbackRef.current\n          );\n        };\n\n        const onError = (error: Error) => {\n          subscription.current.unsubscribe();\n          subscription.current = observable.resubscribeAfterError(\n            onNext,\n            onError\n          );\n\n          if (!hasOwnProperty.call(error, \"graphQLErrors\")) {\n            // The error is not a GraphQL error\n            throw error;\n          }\n\n          const previousResult = resultData.current;\n          if (\n            !previousResult ||\n            (previousResult && previousResult.loading) ||\n            !equal(error, previousResult.error)\n          ) {\n            setResult(\n              {\n                data: (previousResult &&\n                  previousResult.data) as MaybeMasked<TData>,\n                error: error as ApolloError,\n                loading: false,\n                networkStatus: NetworkStatus.error,\n              },\n              resultData,\n              observable,\n              client,\n              partialRefetch,\n              handleStoreChange,\n              callbackRef.current\n            );\n          }\n        };\n\n        // TODO evaluate if we keep this in\n        // React Compiler cannot handle scoped `let` access, but a mutable object\n        // like this is fine.\n        // was:\n        // let subscription = observable.subscribe(onNext, onError);\n        const subscription = { current: observable.subscribe(onNext, onError) };\n\n        // Do the \"unsubscribe\" with a short delay.\n        // This way, an existing subscription can be reused without an additional\n        // request if \"unsubscribe\"  and \"resubscribe\" to the same ObservableQuery\n        // happen in very fast succession.\n        return () => {\n          setTimeout(() => subscription.current.unsubscribe());\n        };\n      },\n\n      [\n        disableNetworkFetches,\n        isSyncSSR,\n        observable,\n        resultData,\n        partialRefetch,\n        client,\n      ]\n    ),\n    () =>\n      currentResultOverride ||\n      getCurrentResult(\n        resultData,\n        observable,\n        callbackRef.current,\n        partialRefetch,\n        client\n      ),\n    () =>\n      currentResultOverride ||\n      getCurrentResult(\n        resultData,\n        observable,\n        callbackRef.current,\n        partialRefetch,\n        client\n      )\n  );\n}\n\nfunction useRegisterSSRObservable(\n  observable: ObsQueryWithMeta<any, any>,\n  renderPromises: RenderPromises | undefined,\n  ssrAllowed: boolean\n) {\n  if (renderPromises && ssrAllowed) {\n    renderPromises.registerSSRObservable(observable);\n\n    if (observable.getCurrentResult().loading) {\n      // TODO: This is a legacy API which could probably be cleaned up\n      renderPromises.addObservableQueryPromise(observable);\n    }\n  }\n}\n\n// this hook is not compatible with any rules of React, and there's no good way to rewrite it.\n// it should stay a separate hook that will not be optimized by the compiler\nfunction useResubscribeIfNecessary<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  /** this hook will mutate properties on `resultData` */\n  resultData: InternalResult<TData, TVariables>,\n  /** this hook will mutate properties on `observable` */\n  observable: ObsQueryWithMeta<TData, TVariables>,\n  client: ApolloClient<object>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>,\n  watchQueryOptions: Readonly<WatchQueryOptions<TVariables, TData>>\n) {\n  if (\n    observable[lastWatchOptions] &&\n    !equal(observable[lastWatchOptions], watchQueryOptions)\n  ) {\n    // Though it might be tempting to postpone this reobserve call to the\n    // useEffect block, we need getCurrentResult to return an appropriate\n    // loading:true result synchronously (later within the same call to\n    // useQuery). Since we already have this.observable here (not true for\n    // the very first call to useQuery), we are not initiating any new\n    // subscriptions, though it does feel less than ideal that reobserve\n    // (potentially) kicks off a network request (for example, when the\n    // variables have changed), which is technically a side-effect.\n    observable.reobserve(\n      getObsQueryOptions(observable, client, options, watchQueryOptions)\n    );\n\n    // Make sure getCurrentResult returns a fresh ApolloQueryResult<TData>,\n    // but save the current data as this.previousData, just like setResult\n    // usually does.\n    resultData.previousData =\n      resultData.current?.data || resultData.previousData;\n    resultData.current = void 0;\n  }\n  observable[lastWatchOptions] = watchQueryOptions;\n}\n\n/*\n * A function to massage options before passing them to ObservableQuery.\n * This is two-step curried because we want to reuse the `make` function,\n * but the `observable` might differ between calls to `make`.\n */\nexport function createMakeWatchQueryOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  client: ApolloClient<object>,\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  {\n    skip,\n    ssr,\n    onCompleted,\n    onError,\n    defaultOptions,\n    // The above options are useQuery-specific, so this ...otherOptions spread\n    // makes otherOptions almost a WatchQueryOptions object, except for the\n    // query property that we add below.\n    ...otherOptions\n  }: QueryHookOptions<TData, TVariables> = {},\n  isSyncSSR: boolean\n) {\n  return (\n    observable?: ObservableQuery<TData, TVariables>\n  ): WatchQueryOptions<TVariables, TData> => {\n    // This Object.assign is safe because otherOptions is a fresh ...rest object\n    // that did not exist until just now, so modifications are still allowed.\n    const watchQueryOptions: WatchQueryOptions<TVariables, TData> =\n      Object.assign(otherOptions, { query });\n\n    if (\n      isSyncSSR &&\n      (watchQueryOptions.fetchPolicy === \"network-only\" ||\n        watchQueryOptions.fetchPolicy === \"cache-and-network\")\n    ) {\n      // this behavior was added to react-apollo without explanation in this PR\n      // https://github.com/apollographql/react-apollo/pull/1579\n      watchQueryOptions.fetchPolicy = \"cache-first\";\n    }\n\n    if (!watchQueryOptions.variables) {\n      watchQueryOptions.variables = {} as TVariables;\n    }\n\n    if (skip) {\n      // When skipping, we set watchQueryOptions.fetchPolicy initially to\n      // \"standby\", but we also need/want to preserve the initial non-standby\n      // fetchPolicy that would have been used if not skipping.\n      watchQueryOptions.initialFetchPolicy =\n        watchQueryOptions.initialFetchPolicy ||\n        watchQueryOptions.fetchPolicy ||\n        getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n      watchQueryOptions.fetchPolicy = \"standby\";\n    } else if (!watchQueryOptions.fetchPolicy) {\n      watchQueryOptions.fetchPolicy =\n        observable?.options.initialFetchPolicy ||\n        getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n    }\n\n    return watchQueryOptions;\n  };\n}\n\nexport function getObsQueryOptions<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  observable: ObservableQuery<TData, TVariables> | undefined,\n  client: ApolloClient<object>,\n  queryHookOptions: QueryHookOptions<TData, TVariables>,\n  watchQueryOptions: Partial<WatchQueryOptions<TVariables, TData>>\n): WatchQueryOptions<TVariables, TData> {\n  const toMerge: Array<Partial<WatchQueryOptions<TVariables, TData>>> = [];\n\n  const globalDefaults = client.defaultOptions.watchQuery;\n  if (globalDefaults) toMerge.push(globalDefaults);\n\n  if (queryHookOptions.defaultOptions) {\n    toMerge.push(queryHookOptions.defaultOptions);\n  }\n\n  // We use compact rather than mergeOptions for this part of the merge,\n  // because we want watchQueryOptions.variables (if defined) to replace\n  // this.observable.options.variables whole. This replacement allows\n  // removing variables by removing them from the variables input to\n  // useQuery. If the variables were always merged together (rather than\n  // replaced), there would be no way to remove existing variables.\n  // However, the variables from options.defaultOptions and globalDefaults\n  // (if provided) should be merged, to ensure individual defaulted\n  // variables always have values, if not otherwise defined in\n  // observable.options or watchQueryOptions.\n  toMerge.push(compact(observable && observable.options, watchQueryOptions));\n\n  return toMerge.reduce(mergeOptions) as WatchQueryOptions<TVariables, TData>;\n}\n\nfunction setResult<TData, TVariables extends OperationVariables>(\n  nextResult: ApolloQueryResult<MaybeMasked<TData>>,\n  resultData: InternalResult<TData, TVariables>,\n  observable: ObservableQuery<TData, TVariables>,\n  client: ApolloClient<object>,\n  partialRefetch: boolean | undefined,\n  forceUpdate: () => void,\n  callbacks: Callbacks<TData>\n) {\n  const previousResult = resultData.current;\n  if (previousResult && previousResult.data) {\n    resultData.previousData = previousResult.data;\n  }\n\n  if (!nextResult.error && isNonEmptyArray(nextResult.errors)) {\n    // Until a set naming convention for networkError and graphQLErrors is\n    // decided upon, we map errors (graphQLErrors) to the error options.\n    // TODO: Is it possible for both result.error and result.errors to be\n    // defined here?\n    nextResult.error = new ApolloError({ graphQLErrors: nextResult.errors });\n  }\n\n  resultData.current = toQueryResult(\n    unsafeHandlePartialRefetch(nextResult, observable, partialRefetch),\n    resultData.previousData,\n    observable,\n    client\n  );\n  // Calling state.setResult always triggers an update, though some call sites\n  // perform additional equality checks before committing to an update.\n  forceUpdate();\n  handleErrorOrCompleted(nextResult, previousResult?.networkStatus, callbacks);\n}\n\nfunction handleErrorOrCompleted<TData>(\n  result: ApolloQueryResult<MaybeMasked<TData>>,\n  previousNetworkStatus: NetworkStatus | undefined,\n  callbacks: Callbacks<TData>\n) {\n  if (!result.loading) {\n    const error = toApolloError(result);\n\n    // wait a tick in case we are in the middle of rendering a component\n    Promise.resolve()\n      .then(() => {\n        if (error) {\n          callbacks.onError(error);\n        } else if (\n          result.data &&\n          previousNetworkStatus !== result.networkStatus &&\n          result.networkStatus === NetworkStatus.ready\n        ) {\n          callbacks.onCompleted(result.data);\n        }\n      })\n      .catch((error) => {\n        invariant.warn(error);\n      });\n  }\n}\n\nfunction getCurrentResult<TData, TVariables extends OperationVariables>(\n  resultData: InternalResult<TData, TVariables>,\n  observable: ObservableQuery<TData, TVariables>,\n  callbacks: Callbacks<TData>,\n  partialRefetch: boolean | undefined,\n  client: ApolloClient<object>\n): InternalQueryResult<TData, TVariables> {\n  // Using this.result as a cache ensures getCurrentResult continues returning\n  // the same (===) result object, unless state.setResult has been called, or\n  // we're doing server rendering and therefore override the result below.\n  if (!resultData.current) {\n    // WARNING: SIDE-EFFECTS IN THE RENDER FUNCTION\n    // this could call unsafeHandlePartialRefetch\n    setResult(\n      observable.getCurrentResult(),\n      resultData,\n      observable,\n      client,\n      partialRefetch,\n      () => {},\n      callbacks\n    );\n  }\n  return resultData.current!;\n}\n\nexport function getDefaultFetchPolicy<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  queryHookDefaultOptions?: Partial<WatchQueryOptions<TVariables, TData>>,\n  clientDefaultOptions?: DefaultOptions\n): WatchQueryFetchPolicy {\n  return (\n    queryHookDefaultOptions?.fetchPolicy ||\n    clientDefaultOptions?.watchQuery?.fetchPolicy ||\n    \"cache-first\"\n  );\n}\n\nexport function toApolloError<TData>(\n  result: Pick<ApolloQueryResult<TData>, \"errors\" | \"error\">\n): ApolloError | undefined {\n  return isNonEmptyArray(result.errors) ?\n      new ApolloError({ graphQLErrors: result.errors })\n    : result.error;\n}\n\nexport function toQueryResult<TData, TVariables extends OperationVariables>(\n  result: ApolloQueryResult<MaybeMasked<TData>>,\n  previousData: MaybeMasked<TData> | undefined,\n  observable: ObservableQuery<TData, TVariables>,\n  client: ApolloClient<object>\n): InternalQueryResult<TData, TVariables> {\n  const { data, partial, ...resultWithoutPartial } = result;\n  const queryResult: InternalQueryResult<TData, TVariables> = {\n    data, // Ensure always defined, even if result.data is missing.\n    ...resultWithoutPartial,\n    client: client,\n    observable: observable,\n    variables: observable.variables,\n    called: result !== ssrDisabledResult && result !== skipStandbyResult,\n    previousData,\n  };\n  return queryResult;\n}\n\nfunction unsafeHandlePartialRefetch<\n  TData,\n  TVariables extends OperationVariables,\n>(\n  result: ApolloQueryResult<MaybeMasked<TData>>,\n  observable: ObservableQuery<TData, TVariables>,\n  partialRefetch: boolean | undefined\n): ApolloQueryResult<MaybeMasked<TData>> {\n  // TODO: This code should be removed when the partialRefetch option is\n  // removed. I was unable to get this hook to behave reasonably in certain\n  // edge cases when this block was put in an effect.\n  if (\n    result.partial &&\n    partialRefetch &&\n    !result.loading &&\n    (!result.data || Object.keys(result.data).length === 0) &&\n    observable.options.fetchPolicy !== \"cache-only\"\n  ) {\n    observable.refetch();\n    return {\n      ...result,\n      loading: true,\n      networkStatus: NetworkStatus.refetch,\n    };\n  }\n  return result;\n}\n\nconst ssrDisabledResult = maybeDeepFreeze({\n  loading: true,\n  data: void 0 as any,\n  error: void 0,\n  networkStatus: NetworkStatus.loading,\n});\n\nconst skipStandbyResult = maybeDeepFreeze({\n  loading: false,\n  data: void 0 as any,\n  error: void 0,\n  networkStatus: NetworkStatus.ready,\n});\n\nfunction bindObservableMethods<TData, TVariables extends OperationVariables>(\n  observable: ObservableQuery<TData, TVariables>\n): Omit<ObservableQueryFields<TData, TVariables>, \"variables\"> {\n  return {\n    refetch: observable.refetch.bind(observable),\n    reobserve: observable.reobserve.bind(observable),\n    fetchMore: observable.fetchMore.bind(observable),\n    updateQuery: observable.updateQuery.bind(observable),\n    startPolling: observable.startPolling.bind(observable),\n    stopPolling: observable.stopPolling.bind(observable),\n    subscribeToMore: observable.subscribeToMore.bind(observable),\n  };\n}\n"], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;AAkBA;AACA,SAASA,SAAS,QAAQ,kCAAkC;AAE5D,OAAO,KAAKC,KAAK,MAAM,SAAS;AAChC,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,KAAK,QAAQ,eAAe;AAErC,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,WAAW,QAAQ,uBAAuB;AAWnD,SAASC,eAAe,EAAEC,aAAa,QAAQ,qBAAqB;AAQpE,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,oBAAoB;AACrE,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SACEC,OAAO,EACPC,eAAe,EACfC,eAAe,QACV,0BAA0B;AACjC,SAASC,QAAQ,QAAQ,qBAAqB;AAK/B,IAAAC,cAAc,GACzBC,MAAM,CAAAC,SAAA,CAAAF,cADmB;AAQ7B,SAASG,IAAIA,CAAA,GAAI;AACjB,IAAMC,gBAAgB,GAAGC,MAAM,EAAE;AAkCjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,OAAM,SAAUC,QAAQA,CAItBC,KAA0D,EAC1DC,OAGuB;EAHvB,IAAAA,OAAA;IAAAA,OAAA,GAGIP,MAAM,CAACQ,MAAM,CAAC,IAAI,CAAC;EAAA;EAEvB,OAAOV,QAAQ,CACb,UAAU;EACV;EACAW,SAAS,EACTf,eAAe,CAACa,OAAO,IAAIA,OAAO,CAACG,MAAM,CAAC,CAC3C,CAACJ,KAAK,EAAEC,OAAO,CAAC;AACnB;AAEA,SAASE,SAASA,CAIhBH,KAA0D,EAC1DC,OAA8D;EAExD,IAAAI,EAAA,GAA6BC,iBAAiB,CAACN,KAAK,EAAEC,OAAO,CAAC;IAA5DM,MAAM,GAAAF,EAAA,CAAAE,MAAA;IAAEC,cAAc,GAAAH,EAAA,CAAAG,cAAsC;EACpE,OAAO9B,KAAK,CAAC+B,OAAO,CAClB;IAAM,OAAAC,QAAA,CAAAA,QAAA,KAAMH,MAAM,GAAKC,cAAc;EAA/B,CAAkC,EACxC,CAACD,MAAM,EAAEC,cAAc,CAAC,CACzB;AACH;AAEA,SAASG,gBAAgBA,CAIvBP,MAA4B,EAC5BJ,KAAiD,EACjDC,OAA8D,EAC9DW,cAA0C,EAC1CC,qBAAiE;EAEjE,SAASC,mBAAmBA,CAACC,QAA2C;;IACtE5B,kBAAkB,CAACa,KAAK,EAAEd,YAAY,CAAC8B,KAAK,CAAC;IAE7C,IAAMC,aAAa,GAAqC;MACtDb,MAAM,EAAAA,MAAA;MACNJ,KAAK,EAAAA,KAAA;MACLkB,UAAU;MACR;MACA;MACA;MACCN,cAAc,IACbA,cAAc,CAACO,gBAAgB,CAACN,qBAAqB,EAAE,CAAC,IAC1D7B,eAAe,CAAC,oBAAoB,CAAC,CAACoC,SAAS,CAAC,CAACR,cAAc,EAAE;QAC/D,OAAAR,MAAM,CAACiB,UAAU,CACfC,kBAAkB,CAAC,KAAK,CAAC,EAAElB,MAAM,EAAEH,OAAO,EAAEY,qBAAqB,EAAE,CAAC,CACrE;MAFD,CAEC,CACF;MACHU,UAAU,EAAE;QACV;QACA;QACAC,YAAY,EAAE,CAAAnB,EAAA,GAAAU,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,UAAU,CAACE,OAAO,cAAApB,EAAA,uBAAAA,EAAA,CAAEqB;;KAE/C;IAED,OAAOT,aAAiD;EAC1D;EAEI,IAAAZ,EAAA,GACF3B,KAAK,CAACiD,QAAQ,CAACb,mBAAmB,CAAC;IADhCG,aAAa,GAAAZ,EAAA;IAAEuB,mBAAmB,GAAAvB,EAAA,GACF;EAErC;;;;;EAKA,SAASwB,eAAeA,CACtBC,iBAAuD;;;IAEvD;IACA;IACApC,MAAM,CAACqC,MAAM,CAACd,aAAa,CAACC,UAAU,GAAAb,EAAA,OACpCA,EAAA,CAACR,gBAAgB,IAAGiC,iBAAiB,E,IACrC;IACF,IAAMP,UAAU,GAAGN,aAAa,CAACM,UAAU;IAC3CK,mBAAmB,CAAAlB,QAAA,CAAAA,QAAA,KACdO,aAAa;MAChB;MACAjB,KAAK,EAAE8B,iBAAiB,CAAC9B,KAAK;MAC9BuB,UAAU,EAAE7B,MAAM,CAACqC,MAAM,CAACR,UAAU,EAAE;QACpC;QACA;QACAC,YAAY,EAAE,EAAAQ,EAAA,GAAAT,UAAU,CAACE,OAAO,cAAAO,EAAA,uBAAAA,EAAA,CAAEN,IAAI,KAAIH,UAAU,CAACC,YAAY;QACjEC,OAAO,EAAEQ;OACV;IAAC,GACF;EACJ;EAEA,IAAI7B,MAAM,KAAKa,aAAa,CAACb,MAAM,IAAIJ,KAAK,KAAKiB,aAAa,CAACjB,KAAK,EAAE;IACpE;IACA;IACA;IACA;IACA;IACA;IACA,IAAMkC,gBAAgB,GAAGpB,mBAAmB,CAACG,aAAa,CAAC;IAC3DW,mBAAmB,CAACM,gBAAgB,CAAC;IACrC,OAAO,CAACA,gBAAgB,EAAEL,eAAe,CAAU;EACrD;EAEA,OAAO,CAACZ,aAAa,EAAEY,eAAe,CAAU;AAClD;AAEA,OAAM,SAAUvB,iBAAiBA,CAI/BN,KAA0D,EAC1DC,OAA8D;EAE9D,IAAMG,MAAM,GAAGhB,eAAe,CAACa,OAAO,CAACG,MAAM,CAAC;EAE9C,IAAMQ,cAAc,GAAGlC,KAAK,CAACyD,UAAU,CAACrD,gBAAgB,EAAE,CAAC,CAAC8B,cAAc;EAC1E,IAAMwB,SAAS,GAAG,CAAC,CAACxB,cAAc;EAClC,IAAMyB,qBAAqB,GAAGjC,MAAM,CAACiC,qBAAqB;EAC1D,IAAMC,UAAU,GAAGrC,OAAO,CAACsC,GAAG,KAAK,KAAK,IAAI,CAACtC,OAAO,CAACuC,IAAI;EACzD,IAAMC,cAAc,GAAGxC,OAAO,CAACwC,cAAc;EAE7C,IAAM5B,qBAAqB,GAAG6B,2BAA2B,CACvDtC,MAAM,EACNJ,KAAK,EACLC,OAAO,EACPmC,SAAS,CACV;EAEK,IAAA/B,EAAA,GAAgDM,gBAAgB,CACpEP,MAAM,EACNJ,KAAK,EACLC,OAAO,EACPW,cAAc,EACdC,qBAAqB,CACtB;IANMmB,EAAA,GAAA3B,EAAA,GAA0B;IAAxBa,UAAU,GAAAc,EAAA,CAAAd,UAAA;IAAEK,UAAU,GAAAS,EAAA,CAAAT,UAAA;IAAIM,eAAe,GAAAxB,EAAA,GAMjD;EAED,IAAMyB,iBAAiB,GACrBjB,qBAAqB,CAACK,UAAU,CAAC;EAEnCyB,yBAAyB,CACvBpB,UAAU;EAAE;EACZL,UAAU;EAAE;EACZd,MAAM,EACNH,OAAO,EACP6B,iBAAiB,CAClB;EAED,IAAMtB,cAAc,GAAG9B,KAAK,CAAC+B,OAAO,CAClC;IAAM,OAAAmC,qBAAqB,CAAC1B,UAAU,CAAC;EAAjC,CAAiC,EACvC,CAACA,UAAU,CAAC,CACb;EAED2B,wBAAwB,CAAC3B,UAAU,EAAEN,cAAc,EAAE0B,UAAU,CAAC;EAEhE,IAAM/B,MAAM,GAAGuC,+BAA+B,CAC5CvB,UAAU,EACVL,UAAU,EACVd,MAAM,EACNH,OAAO,EACP6B,iBAAiB,EACjBO,qBAAqB,EACrBI,cAAc,EACdL,SAAS,EACT;IACEW,WAAW,EAAE9C,OAAO,CAAC8C,WAAW,IAAInD,IAAI;IACxCoD,OAAO,EAAE/C,OAAO,CAAC+C,OAAO,IAAIpD;GAC7B,CACF;EAED,OAAO;IACLW,MAAM,EAAAA,MAAA;IACNC,cAAc,EAAAA,cAAA;IACdU,UAAU,EAAAA,UAAA;IACVK,UAAU,EAAAA,UAAA;IACVnB,MAAM,EAAAA,MAAA;IACNyB,eAAe,EAAAA;GAChB;AACH;AAEA,SAASiB,+BAA+BA,CAItCvB,UAA6C,EAC7CL,UAA8C,EAC9Cd,MAA4B,EAC5BH,OAA8D,EAC9D6B,iBAAiE,EACjEO,qBAA8B,EAC9BI,cAAmC,EACnCL,SAAkB,EAClBa,SAGC;EAED,IAAMC,WAAW,GAAGxE,KAAK,CAACyE,MAAM,CAAmBF,SAAS,CAAC;EAC7DvE,KAAK,CAAC0E,SAAS,CAAC;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACAF,WAAW,CAACzB,OAAO,GAAGwB,SAAS;EACjC,CAAC,CAAC;EAEF,IAAMI,cAAc,GAEhB,CAACjB,SAAS,IAAIC,qBAAqB,KACnCpC,OAAO,CAACsC,GAAG,KAAK,KAAK,IACrB,CAACtC,OAAO,CAACuC,IAAI;EAEb;EACA;EACAc,iBAAiB,GACjBrD,OAAO,CAACuC,IAAI,IAAIV,iBAAiB,CAACyB,WAAW,KAAK,SAAS;EAC3D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,iBAAiB,GACjB,KAAK,CAAC;EAEV,IAAMhC,YAAY,GAAGD,UAAU,CAACC,YAAY;EAC5C,IAAMiC,qBAAqB,GAAG/E,KAAK,CAAC+B,OAAO,CACzC;IACE,OAAA4C,cAAc,IACdK,aAAa,CAACL,cAAc,EAAE7B,YAAY,EAAEN,UAAU,EAAEd,MAAM,CAAC;EAD/D,CAC+D,EACjE,CAACA,MAAM,EAAEc,UAAU,EAAEmC,cAAc,EAAE7B,YAAY,CAAC,CACnD;EAED,OAAO7C,oBAAoB,CACzBD,KAAK,CAACiF,WAAW,CACf,UAACC,iBAAiB;IAChB;IACA;IACAvB,qBAAqB;IAErB,IAAID,SAAS,EAAE;MACb,OAAO,aAAO,CAAC;IACjB;IAEA,IAAMyB,MAAM,GAAG,SAAAA,CAAA;MACb,IAAMC,cAAc,GAAGvC,UAAU,CAACE,OAAO;MACzC;MACA;MACA;MACA,IAAMlB,MAAM,GAAGW,UAAU,CAAC6C,gBAAgB,EAAE;MAC5C;MACA,IACED,cAAc,IACdA,cAAc,CAACE,OAAO,KAAKzD,MAAM,CAACyD,OAAO,IACzCF,cAAc,CAACG,aAAa,KAAK1D,MAAM,CAAC0D,aAAa,IACrDrF,KAAK,CAACkF,cAAc,CAACpC,IAAI,EAAEnB,MAAM,CAACmB,IAAI,CAAC,EACvC;QACA;MACF;MAEAwC,SAAS,CACP3D,MAAM,EACNgB,UAAU,EACVL,UAAU,EACVd,MAAM,EACNqC,cAAc,EACdmB,iBAAiB,EACjBV,WAAW,CAACzB,OAAO,CACpB;IACH,CAAC;IAED,IAAMuB,OAAO,GAAG,SAAAA,CAACmB,KAAY;MAC3BC,YAAY,CAAC3C,OAAO,CAAC4C,WAAW,EAAE;MAClCD,YAAY,CAAC3C,OAAO,GAAGP,UAAU,CAACoD,qBAAqB,CACrDT,MAAM,EACNb,OAAO,CACR;MAED,IAAI,CAACvD,cAAc,CAAC8E,IAAI,CAACJ,KAAK,EAAE,eAAe,CAAC,EAAE;QAChD;QACA,MAAMA,KAAK;MACb;MAEA,IAAML,cAAc,GAAGvC,UAAU,CAACE,OAAO;MACzC,IACE,CAACqC,cAAc,IACdA,cAAc,IAAIA,cAAc,CAACE,OAAQ,IAC1C,CAACpF,KAAK,CAACuF,KAAK,EAAEL,cAAc,CAACK,KAAK,CAAC,EACnC;QACAD,SAAS,CACP;UACExC,IAAI,EAAGoC,cAAc,IACnBA,cAAc,CAACpC,IAA2B;UAC5CyC,KAAK,EAAEA,KAAoB;UAC3BH,OAAO,EAAE,KAAK;UACdC,aAAa,EAAEhF,aAAa,CAACkF;SAC9B,EACD5C,UAAU,EACVL,UAAU,EACVd,MAAM,EACNqC,cAAc,EACdmB,iBAAiB,EACjBV,WAAW,CAACzB,OAAO,CACpB;MACH;IACF,CAAC;IAED;IACA;IACA;IACA;IACA;IACA,IAAM2C,YAAY,GAAG;MAAE3C,OAAO,EAAEP,UAAU,CAACsD,SAAS,CAACX,MAAM,EAAEb,OAAO;IAAC,CAAE;IAEvE;IACA;IACA;IACA;IACA,OAAO;MACLyB,UAAU,CAAC;QAAM,OAAAL,YAAY,CAAC3C,OAAO,CAAC4C,WAAW,EAAE;MAAlC,CAAkC,CAAC;IACtD,CAAC;EACH,CAAC,EAED,CACEhC,qBAAqB,EACrBD,SAAS,EACTlB,UAAU,EACVK,UAAU,EACVkB,cAAc,EACdrC,MAAM,CACP,CACF,EACD;IACE,OAAAqD,qBAAqB,IACrBM,gBAAgB,CACdxC,UAAU,EACVL,UAAU,EACVgC,WAAW,CAACzB,OAAO,EACnBgB,cAAc,EACdrC,MAAM,CACP;EAPD,CAOC,EACH;IACE,OAAAqD,qBAAqB,IACrBM,gBAAgB,CACdxC,UAAU,EACVL,UAAU,EACVgC,WAAW,CAACzB,OAAO,EACnBgB,cAAc,EACdrC,MAAM,CACP;EAPD,CAOC,CACJ;AACH;AAEA,SAASyC,wBAAwBA,CAC/B3B,UAAsC,EACtCN,cAA0C,EAC1C0B,UAAmB;EAEnB,IAAI1B,cAAc,IAAI0B,UAAU,EAAE;IAChC1B,cAAc,CAAC8D,qBAAqB,CAACxD,UAAU,CAAC;IAEhD,IAAIA,UAAU,CAAC6C,gBAAgB,EAAE,CAACC,OAAO,EAAE;MACzC;MACApD,cAAc,CAAC+D,yBAAyB,CAACzD,UAAU,CAAC;IACtD;EACF;AACF;AAEA;AACA;AACA,SAASyB,yBAAyBA,CAIhC;AACApB,UAA6C,EAC7C;AACAL,UAA+C,EAC/Cd,MAA4B,EAC5BH,OAA8D,EAC9D6B,iBAAiE;;EAEjE,IACEZ,UAAU,CAACrB,gBAAgB,CAAC,IAC5B,CAACjB,KAAK,CAACsC,UAAU,CAACrB,gBAAgB,CAAC,EAAEiC,iBAAiB,CAAC,EACvD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAZ,UAAU,CAAC0D,SAAS,CAClBtD,kBAAkB,CAACJ,UAAU,EAAEd,MAAM,EAAEH,OAAO,EAAE6B,iBAAiB,CAAC,CACnE;IAED;IACA;IACA;IACAP,UAAU,CAACC,YAAY,GACrB,EAAAnB,EAAA,GAAAkB,UAAU,CAACE,OAAO,cAAApB,EAAA,uBAAAA,EAAA,CAAEqB,IAAI,KAAIH,UAAU,CAACC,YAAY;IACrDD,UAAU,CAACE,OAAO,GAAG,KAAK,CAAC;EAC7B;EACAP,UAAU,CAACrB,gBAAgB,CAAC,GAAGiC,iBAAiB;AAClD;AAEA;;;;;AAKA,OAAM,SAAUY,2BAA2BA,CAIzCtC,MAA4B,EAC5BJ,KAA0D,EAC1DK,EAU2C,EAC3C+B,SAAkB;EAXlB,IAAA/B,EAAA;IAAAA,EAAA,KAU2C;EAAA;EATzC,IAAAmC,IAAI,GAAAnC,EAAA,CAAAmC,IAAA;IACJD,GAAG,GAAAlC,EAAA,CAAAkC,GAAA;IACHQ,WAAW,GAAA1C,EAAA,CAAA0C,WAAA;IACXC,OAAO,GAAA3C,EAAA,CAAA2C,OAAA;IACP6B,cAAc,GAAAxE,EAAA,CAAAwE,cAAA;IACd;IACA;IACA;IACGC,YAAY,GAAAC,MAAA,CAAA1E,EAAA,EATjB,2DAUC,CADgB;EAIjB,OAAO,UACLa,UAA+C;IAE/C;IACA;IACA,IAAMY,iBAAiB,GACrBpC,MAAM,CAACqC,MAAM,CAAC+C,YAAY,EAAE;MAAE9E,KAAK,EAAAA;IAAA,CAAE,CAAC;IAExC,IACEoC,SAAS,KACRN,iBAAiB,CAACyB,WAAW,KAAK,cAAc,IAC/CzB,iBAAiB,CAACyB,WAAW,KAAK,mBAAmB,CAAC,EACxD;MACA;MACA;MACAzB,iBAAiB,CAACyB,WAAW,GAAG,aAAa;IAC/C;IAEA,IAAI,CAACzB,iBAAiB,CAACkD,SAAS,EAAE;MAChClD,iBAAiB,CAACkD,SAAS,GAAG,EAAgB;IAChD;IAEA,IAAIxC,IAAI,EAAE;MACR;MACA;MACA;MACAV,iBAAiB,CAACmD,kBAAkB,GAClCnD,iBAAiB,CAACmD,kBAAkB,IACpCnD,iBAAiB,CAACyB,WAAW,IAC7B2B,qBAAqB,CAACL,cAAc,EAAEzE,MAAM,CAACyE,cAAc,CAAC;MAC9D/C,iBAAiB,CAACyB,WAAW,GAAG,SAAS;IAC3C,CAAC,MAAM,IAAI,CAACzB,iBAAiB,CAACyB,WAAW,EAAE;MACzCzB,iBAAiB,CAACyB,WAAW,GAC3B,CAAArC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjB,OAAO,CAACgF,kBAAkB,KACtCC,qBAAqB,CAACL,cAAc,EAAEzE,MAAM,CAACyE,cAAc,CAAC;IAChE;IAEA,OAAO/C,iBAAiB;EAC1B,CAAC;AACH;AAEA,OAAM,SAAUR,kBAAkBA,CAIhCJ,UAA0D,EAC1Dd,MAA4B,EAC5B+E,gBAAqD,EACrDrD,iBAAgE;EAEhE,IAAMsD,OAAO,GAAyD,EAAE;EAExE,IAAMC,cAAc,GAAGjF,MAAM,CAACyE,cAAc,CAACxD,UAAU;EACvD,IAAIgE,cAAc,EAAED,OAAO,CAACE,IAAI,CAACD,cAAc,CAAC;EAEhD,IAAIF,gBAAgB,CAACN,cAAc,EAAE;IACnCO,OAAO,CAACE,IAAI,CAACH,gBAAgB,CAACN,cAAc,CAAC;EAC/C;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAO,OAAO,CAACE,IAAI,CAACjG,OAAO,CAAC6B,UAAU,IAAIA,UAAU,CAACjB,OAAO,EAAE6B,iBAAiB,CAAC,CAAC;EAE1E,OAAOsD,OAAO,CAACG,MAAM,CAAC1G,YAAY,CAAyC;AAC7E;AAEA,SAASqF,SAASA,CAChBsB,UAAiD,EACjDjE,UAA6C,EAC7CL,UAA8C,EAC9Cd,MAA4B,EAC5BqC,cAAmC,EACnCgD,WAAuB,EACvBxC,SAA2B;EAE3B,IAAMa,cAAc,GAAGvC,UAAU,CAACE,OAAO;EACzC,IAAIqC,cAAc,IAAIA,cAAc,CAACpC,IAAI,EAAE;IACzCH,UAAU,CAACC,YAAY,GAAGsC,cAAc,CAACpC,IAAI;EAC/C;EAEA,IAAI,CAAC8D,UAAU,CAACrB,KAAK,IAAI7E,eAAe,CAACkG,UAAU,CAACE,MAAM,CAAC,EAAE;IAC3D;IACA;IACA;IACA;IACAF,UAAU,CAACrB,KAAK,GAAG,IAAIpF,WAAW,CAAC;MAAE4G,aAAa,EAAEH,UAAU,CAACE;IAAM,CAAE,CAAC;EAC1E;EAEAnE,UAAU,CAACE,OAAO,GAAGiC,aAAa,CAChCkC,0BAA0B,CAACJ,UAAU,EAAEtE,UAAU,EAAEuB,cAAc,CAAC,EAClElB,UAAU,CAACC,YAAY,EACvBN,UAAU,EACVd,MAAM,CACP;EACD;EACA;EACAqF,WAAW,EAAE;EACbI,sBAAsB,CAACL,UAAU,EAAE1B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,aAAa,EAAEhB,SAAS,CAAC;AAC9E;AAEA,SAAS4C,sBAAsBA,CAC7BtF,MAA6C,EAC7CuF,qBAAgD,EAChD7C,SAA2B;EAE3B,IAAI,CAAC1C,MAAM,CAACyD,OAAO,EAAE;IACnB,IAAM+B,OAAK,GAAGC,aAAa,CAACzF,MAAM,CAAC;IAEnC;IACA0F,OAAO,CAACC,OAAO,EAAE,CACdC,IAAI,CAAC;MACJ,IAAIJ,OAAK,EAAE;QACT9C,SAAS,CAACD,OAAO,CAAC+C,OAAK,CAAC;MAC1B,CAAC,MAAM,IACLxF,MAAM,CAACmB,IAAI,IACXoE,qBAAqB,KAAKvF,MAAM,CAAC0D,aAAa,IAC9C1D,MAAM,CAAC0D,aAAa,KAAKhF,aAAa,CAACmH,KAAK,EAC5C;QACAnD,SAAS,CAACF,WAAW,CAACxC,MAAM,CAACmB,IAAI,CAAC;MACpC;IACF,CAAC,CAAC,CACD2E,KAAK,CAAC,UAAClC,KAAK;MACXmC,UAAU,CAAAC,OAAK,KAAO,SAAA9H,SAAA,CAAA+H,IAAA,CAAArC,KAAA;IACxB,CAAC,CAAC;EACN;AACF;AAEA,SAASJ,gBAAgBA,CACvBxC,UAA6C,EAC7CL,UAA8C,EAC9C+B,SAA2B,EAC3BR,cAAmC,EACnCrC,MAA4B;EAE5B;EACA;EACA;EACA,IAAI,CAACmB,UAAU,CAACE,OAAO,EAAE;IACvB;IACA;IACAyC,SAAS,CACPhD,UAAU,CAAC6C,gBAAgB,EAAE,EAC7BxC,UAAU,EACVL,UAAU,EACVd,MAAM,EACNqC,cAAc,EACd,aAAO,CAAC,EACRQ,SAAS,CACV;EACH;EACA,OAAO1B,UAAU,CAACE,OAAQ;AAC5B;AAEA,OAAM,SAAUyD,qBAAqBA,CAInCuB,uBAAuE,EACvEC,oBAAqC;;EAErC,OACE,CAAAD,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAElD,WAAW,MACpC,CAAAlD,EAAA,GAAAqG,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAErF,UAAU,cAAAhB,EAAA,uBAAAA,EAAA,CAAEkD,WAAW,KAC7C,aAAa;AAEjB;AAEA,OAAM,SAAUyC,aAAaA,CAC3BzF,MAA0D;EAE1D,OAAOjB,eAAe,CAACiB,MAAM,CAACmF,MAAM,CAAC,GACjC,IAAI3G,WAAW,CAAC;IAAE4G,aAAa,EAAEpF,MAAM,CAACmF;EAAM,CAAE,CAAC,GACjDnF,MAAM,CAAC4D,KAAK;AAClB;AAEA,OAAM,SAAUT,aAAaA,CAC3BnD,MAA6C,EAC7CiB,YAA4C,EAC5CN,UAA8C,EAC9Cd,MAA4B;EAEpB,IAAAsB,IAAI,GAAuCnB,MAAM,CAAAmB,IAA7C;IAAEiF,OAAO,GAA8BpG,MAAM,CAAAoG,OAApC;IAAKC,oBAAoB,GAAA7B,MAAA,CAAKxE,MAAM,EAAnD,mBAA0C,CAAF;EAC9C,IAAMsG,WAAW,GAAAnG,QAAA,CAAAA,QAAA;IACfgB,IAAI,EAAAA;EAAA,GACDkF,oBAAoB;IACvBxG,MAAM,EAAEA,MAAM;IACdc,UAAU,EAAEA,UAAU;IACtB8D,SAAS,EAAE9D,UAAU,CAAC8D,SAAS;IAC/B8B,MAAM,EAAEvG,MAAM,KAAK+C,iBAAiB,IAAI/C,MAAM,KAAKiD,iBAAiB;IACpEhC,YAAY,EAAAA;EAAA,EACb;EACD,OAAOqF,WAAW;AACpB;AAEA,SAASjB,0BAA0BA,CAIjCrF,MAA6C,EAC7CW,UAA8C,EAC9CuB,cAAmC;EAEnC;EACA;EACA;EACA,IACElC,MAAM,CAACoG,OAAO,IACdlE,cAAc,IACd,CAAClC,MAAM,CAACyD,OAAO,KACd,CAACzD,MAAM,CAACmB,IAAI,IAAIhC,MAAM,CAACqH,IAAI,CAACxG,MAAM,CAACmB,IAAI,CAAC,CAACsF,MAAM,KAAK,CAAC,CAAC,IACvD9F,UAAU,CAACjB,OAAO,CAACsD,WAAW,KAAK,YAAY,EAC/C;IACArC,UAAU,CAAC+F,OAAO,EAAE;IACpB,OAAAvG,QAAA,CAAAA,QAAA,KACKH,MAAM;MACTyD,OAAO,EAAE,IAAI;MACbC,aAAa,EAAEhF,aAAa,CAACgI;IAAO;EAExC;EACA,OAAO1G,MAAM;AACf;AAEA,IAAM+C,iBAAiB,GAAG/D,eAAe,CAAC;EACxCyE,OAAO,EAAE,IAAI;EACbtC,IAAI,EAAE,KAAK,CAAQ;EACnByC,KAAK,EAAE,KAAK,CAAC;EACbF,aAAa,EAAEhF,aAAa,CAAC+E;CAC9B,CAAC;AAEF,IAAMR,iBAAiB,GAAGjE,eAAe,CAAC;EACxCyE,OAAO,EAAE,KAAK;EACdtC,IAAI,EAAE,KAAK,CAAQ;EACnByC,KAAK,EAAE,KAAK,CAAC;EACbF,aAAa,EAAEhF,aAAa,CAACmH;CAC9B,CAAC;AAEF,SAASxD,qBAAqBA,CAC5B1B,UAA8C;EAE9C,OAAO;IACL+F,OAAO,EAAE/F,UAAU,CAAC+F,OAAO,CAACC,IAAI,CAAChG,UAAU,CAAC;IAC5C0D,SAAS,EAAE1D,UAAU,CAAC0D,SAAS,CAACsC,IAAI,CAAChG,UAAU,CAAC;IAChDiG,SAAS,EAAEjG,UAAU,CAACiG,SAAS,CAACD,IAAI,CAAChG,UAAU,CAAC;IAChDkG,WAAW,EAAElG,UAAU,CAACkG,WAAW,CAACF,IAAI,CAAChG,UAAU,CAAC;IACpDmG,YAAY,EAAEnG,UAAU,CAACmG,YAAY,CAACH,IAAI,CAAChG,UAAU,CAAC;IACtDoG,WAAW,EAAEpG,UAAU,CAACoG,WAAW,CAACJ,IAAI,CAAChG,UAAU,CAAC;IACpDqG,eAAe,EAAErG,UAAU,CAACqG,eAAe,CAACL,IAAI,CAAChG,UAAU;GAC5D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}