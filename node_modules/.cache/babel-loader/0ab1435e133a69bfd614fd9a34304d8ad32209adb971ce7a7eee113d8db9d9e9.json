{"ast": null, "code": "import { Observable } from \"../../utilities/index.js\";\nexport function fromError(errorValue) {\n  return new Observable(function (observer) {\n    observer.error(errorValue);\n  });\n}", "map": {"version": 3, "names": ["Observable", "fromError", "errorValue", "observer", "error"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/link/utils/fromError.ts"], "sourcesContent": ["import { Observable } from \"../../utilities/index.js\";\n\nexport function fromError<T>(errorValue: any): Observable<T> {\n  return new Observable<T>((observer) => {\n    observer.error(errorValue);\n  });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,0BAA0B;AAErD,OAAM,SAAUC,SAASA,CAAIC,UAAe;EAC1C,OAAO,IAAIF,UAAU,CAAI,UAACG,QAAQ;IAChCA,QAAQ,CAACC,KAAK,CAACF,UAAU,CAAC;EAC5B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}