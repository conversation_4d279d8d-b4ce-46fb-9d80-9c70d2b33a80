{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function readerIterator(reader) {\n  var iterator = {\n    next: function () {\n      return reader.read();\n    }\n  };\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function () {\n      return this;\n    };\n  }\n  return iterator;\n}", "map": {"version": 3, "names": ["canUseAsyncIteratorSymbol", "readerIterator", "reader", "iterator", "next", "read", "Symbol", "asyncIterator"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/node_modules/@apollo/src/link/http/iterators/reader.ts"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\n\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\n\ninterface ReaderIterator<T> {\n  next(): Promise<IteratorResult<T, T | undefined>>;\n  [Symbol.asyncIterator]?(): AsyncIterator<T>;\n}\n\nexport default function readerIterator<T>(\n  reader: ReadableStreamDefaultReader<T>\n): AsyncIterableIterator<T> {\n  const iterator: ReaderIterator<T> = {\n    next() {\n      return reader.read() as Promise<\n        | ReadableStreamReadValueResult<T>\n        // DoneR<PERSON>ult has `value` optional, which doesn't comply with an\n        // `IteratorResult`, so we assert it to `T | undefined` instead\n        | Required<ReadableStreamReadDoneResult<T | undefined>>\n      >;\n    },\n  };\n\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function (): AsyncIterator<\n      T,\n      T | undefined\n    > {\n      return this;\n    };\n  }\n\n  return iterator as AsyncIterableIterator<T>;\n}\n"], "mappings": "AAAA;;;;AAKA,SAASA,yBAAyB,QAAQ,6BAA6B;AAOvE,eAAc,SAAUC,cAAcA,CACpCC,MAAsC;EAEtC,IAAMC,QAAQ,GAAsB;IAClCC,IAAI,WAAAA,CAAA;MACF,OAAOF,MAAM,CAACG,IAAI,EAKjB;IACH;GACD;EAED,IAAIL,yBAAyB,EAAE;IAC7BG,QAAQ,CAACG,MAAM,CAACC,aAAa,CAAC,GAAG;MAI/B,OAAO,IAAI;IACb,CAAC;EACH;EAEA,OAAOJ,QAAoC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}