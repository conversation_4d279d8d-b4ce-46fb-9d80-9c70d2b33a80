{"ast": null, "code": "import * as yup from 'yup';\n\n// Phone number validation regex\nconst phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n\n// Step 1: Organization creation validation schema\nexport const organizationSchema = yup.object({\n  name: yup.string().required('Organization name is required'),\n  description: yup.string().required('Description is required'),\n  street: yup.string().required('Street address is required'),\n  street2: yup.string(),\n  // Optional field\n  city: yup.string().required('City is required'),\n  state: yup.string().required('State is required'),\n  postalCode: yup.string().required('Postal code is required'),\n  phoneNumber: yup.string().matches(phoneRegex, 'Please enter a valid phone number').required('Phone number is required')\n});\n\n// Step 2: Staff member validation schema\nexport const staffMemberSchema = yup.object({\n  role: yup.string().oneOf(['provider', 'staff', 'admin'], 'Please select a valid role').required('Role is required'),\n  firstName: yup.string().required('First name is required'),\n  lastName: yup.string().required('Last name is required'),\n  email: yup.string().email('Please enter a valid email address').required('Email is required'),\n  phoneNumber: yup.string().matches(phoneRegex, 'Please enter a valid phone number').required('Phone number is required'),\n  dateOfBirth: yup.date().max(new Date(), 'Date of birth cannot be in the future').required('Date of birth is required'),\n  gender: yup.string().oneOf(['male', 'female', 'other'], 'Please select a gender').required('Gender is required')\n});\n\n// Network type options\nexport const networkTypeOptions = [{\n  value: 'facility',\n  label: 'Facility'\n}, {\n  value: 'cbo',\n  label: 'CBO'\n}, {\n  value: 'clinic',\n  label: 'Clinic'\n}, {\n  value: 'hospital',\n  label: 'Hospital'\n}, {\n  value: 'other',\n  label: 'Other'\n}];\n\n// Role options\nexport const roleOptions = [{\n  value: 'provider',\n  label: 'Provider'\n}, {\n  value: 'admin',\n  label: 'Admin'\n}, {\n  value: 'staff',\n  label: 'Staff'\n}];\n\n// Gender options\nexport const genderOptions = [{\n  value: 'male',\n  label: 'Male'\n}, {\n  value: 'female',\n  label: 'Female'\n}, {\n  value: 'other',\n  label: 'Other'\n}];\n\n// US States options\nexport const stateOptions = [{\n  value: 'AL',\n  label: 'Alabama'\n}, {\n  value: 'AK',\n  label: 'Alaska'\n}, {\n  value: 'AZ',\n  label: 'Arizona'\n}, {\n  value: 'AR',\n  label: 'Arkansas'\n}, {\n  value: 'CA',\n  label: 'California'\n}, {\n  value: 'CO',\n  label: 'Colorado'\n}, {\n  value: 'CT',\n  label: 'Connecticut'\n}, {\n  value: 'DE',\n  label: 'Delaware'\n}, {\n  value: 'FL',\n  label: 'Florida'\n}, {\n  value: 'GA',\n  label: 'Georgia'\n}, {\n  value: 'HI',\n  label: 'Hawaii'\n}, {\n  value: 'ID',\n  label: 'Idaho'\n}, {\n  value: 'IL',\n  label: 'Illinois'\n}, {\n  value: 'IN',\n  label: 'Indiana'\n}, {\n  value: 'IA',\n  label: 'Iowa'\n}, {\n  value: 'KS',\n  label: 'Kansas'\n}, {\n  value: 'KY',\n  label: 'Kentucky'\n}, {\n  value: 'LA',\n  label: 'Louisiana'\n}, {\n  value: 'ME',\n  label: 'Maine'\n}, {\n  value: 'MD',\n  label: 'Maryland'\n}, {\n  value: 'MA',\n  label: 'Massachusetts'\n}, {\n  value: 'MI',\n  label: 'Michigan'\n}, {\n  value: 'MN',\n  label: 'Minnesota'\n}, {\n  value: 'MS',\n  label: 'Mississippi'\n}, {\n  value: 'MO',\n  label: 'Missouri'\n}, {\n  value: 'MT',\n  label: 'Montana'\n}, {\n  value: 'NE',\n  label: 'Nebraska'\n}, {\n  value: 'NV',\n  label: 'Nevada'\n}, {\n  value: 'NH',\n  label: 'New Hampshire'\n}, {\n  value: 'NJ',\n  label: 'New Jersey'\n}, {\n  value: 'NM',\n  label: 'New Mexico'\n}, {\n  value: 'NY',\n  label: 'New York'\n}, {\n  value: 'NC',\n  label: 'North Carolina'\n}, {\n  value: 'ND',\n  label: 'North Dakota'\n}, {\n  value: 'OH',\n  label: 'Ohio'\n}, {\n  value: 'OK',\n  label: 'Oklahoma'\n}, {\n  value: 'OR',\n  label: 'Oregon'\n}, {\n  value: 'PA',\n  label: 'Pennsylvania'\n}, {\n  value: 'RI',\n  label: 'Rhode Island'\n}, {\n  value: 'SC',\n  label: 'South Carolina'\n}, {\n  value: 'SD',\n  label: 'South Dakota'\n}, {\n  value: 'TN',\n  label: 'Tennessee'\n}, {\n  value: 'TX',\n  label: 'Texas'\n}, {\n  value: 'UT',\n  label: 'Utah'\n}, {\n  value: 'VT',\n  label: 'Vermont'\n}, {\n  value: 'VA',\n  label: 'Virginia'\n}, {\n  value: 'WA',\n  label: 'Washington'\n}, {\n  value: 'WV',\n  label: 'West Virginia'\n}, {\n  value: 'WI',\n  label: 'Wisconsin'\n}, {\n  value: 'WY',\n  label: 'Wyoming'\n}];", "map": {"version": 3, "names": ["yup", "phoneRegex", "organizationSchema", "object", "name", "string", "required", "description", "street", "street2", "city", "state", "postalCode", "phoneNumber", "matches", "staffMemberSchema", "role", "oneOf", "firstName", "lastName", "email", "dateOfBirth", "date", "max", "Date", "gender", "networkTypeOptions", "value", "label", "roleOptions", "genderOptions", "stateOptions"], "sources": ["/Users/<USER>/Documents/augment-projects/scheduler-admin/src/utils/validation.js"], "sourcesContent": ["import * as yup from 'yup';\n\n// Phone number validation regex\nconst phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n\n// Step 1: Organization creation validation schema\nexport const organizationSchema = yup.object({\n  name: yup.string().required('Organization name is required'),\n  description: yup.string().required('Description is required'),\n  street: yup.string().required('Street address is required'),\n  street2: yup.string(), // Optional field\n  city: yup.string().required('City is required'),\n  state: yup.string().required('State is required'),\n  postalCode: yup.string().required('Postal code is required'),\n  phoneNumber: yup.string()\n    .matches(phoneRegex, 'Please enter a valid phone number')\n    .required('Phone number is required')\n});\n\n// Step 2: Staff member validation schema\nexport const staffMemberSchema = yup.object({\n  role: yup.string()\n    .oneOf(['provider', 'staff', 'admin'], 'Please select a valid role')\n    .required('Role is required'),\n  firstName: yup.string().required('First name is required'),\n  lastName: yup.string().required('Last name is required'),\n  email: yup.string()\n    .email('Please enter a valid email address')\n    .required('Email is required'),\n  phoneNumber: yup.string()\n    .matches(phoneRegex, 'Please enter a valid phone number')\n    .required('Phone number is required'),\n  dateOfBirth: yup.date()\n    .max(new Date(), 'Date of birth cannot be in the future')\n    .required('Date of birth is required'),\n  gender: yup.string()\n    .oneOf(['male', 'female', 'other'], 'Please select a gender')\n    .required('Gender is required')\n});\n\n// Network type options\nexport const networkTypeOptions = [\n  { value: 'facility', label: 'Facility' },\n  { value: 'cbo', label: 'CBO' },\n  { value: 'clinic', label: 'Clinic' },\n  { value: 'hospital', label: 'Hospital' },\n  { value: 'other', label: 'Other' }\n];\n\n// Role options\nexport const roleOptions = [\n  { value: 'provider', label: 'Provider' },\n  { value: 'admin', label: 'Admin' },\n  { value: 'staff', label: 'Staff' }\n];\n\n// Gender options\nexport const genderOptions = [\n  { value: 'male', label: 'Male' },\n  { value: 'female', label: 'Female' },\n  { value: 'other', label: 'Other' }\n];\n\n// US States options\nexport const stateOptions = [\n  { value: 'AL', label: 'Alabama' },\n  { value: 'AK', label: 'Alaska' },\n  { value: 'AZ', label: 'Arizona' },\n  { value: 'AR', label: 'Arkansas' },\n  { value: 'CA', label: 'California' },\n  { value: 'CO', label: 'Colorado' },\n  { value: 'CT', label: 'Connecticut' },\n  { value: 'DE', label: 'Delaware' },\n  { value: 'FL', label: 'Florida' },\n  { value: 'GA', label: 'Georgia' },\n  { value: 'HI', label: 'Hawaii' },\n  { value: 'ID', label: 'Idaho' },\n  { value: 'IL', label: 'Illinois' },\n  { value: 'IN', label: 'Indiana' },\n  { value: 'IA', label: 'Iowa' },\n  { value: 'KS', label: 'Kansas' },\n  { value: 'KY', label: 'Kentucky' },\n  { value: 'LA', label: 'Louisiana' },\n  { value: 'ME', label: 'Maine' },\n  { value: 'MD', label: 'Maryland' },\n  { value: 'MA', label: 'Massachusetts' },\n  { value: 'MI', label: 'Michigan' },\n  { value: 'MN', label: 'Minnesota' },\n  { value: 'MS', label: 'Mississippi' },\n  { value: 'MO', label: 'Missouri' },\n  { value: 'MT', label: 'Montana' },\n  { value: 'NE', label: 'Nebraska' },\n  { value: 'NV', label: 'Nevada' },\n  { value: 'NH', label: 'New Hampshire' },\n  { value: 'NJ', label: 'New Jersey' },\n  { value: 'NM', label: 'New Mexico' },\n  { value: 'NY', label: 'New York' },\n  { value: 'NC', label: 'North Carolina' },\n  { value: 'ND', label: 'North Dakota' },\n  { value: 'OH', label: 'Ohio' },\n  { value: 'OK', label: 'Oklahoma' },\n  { value: 'OR', label: 'Oregon' },\n  { value: 'PA', label: 'Pennsylvania' },\n  { value: 'RI', label: 'Rhode Island' },\n  { value: 'SC', label: 'South Carolina' },\n  { value: 'SD', label: 'South Dakota' },\n  { value: 'TN', label: 'Tennessee' },\n  { value: 'TX', label: 'Texas' },\n  { value: 'UT', label: 'Utah' },\n  { value: 'VT', label: 'Vermont' },\n  { value: 'VA', label: 'Virginia' },\n  { value: 'WA', label: 'Washington' },\n  { value: 'WV', label: 'West Virginia' },\n  { value: 'WI', label: 'Wisconsin' },\n  { value: 'WY', label: 'Wyoming' }\n];"], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,KAAK;;AAE1B;AACA,MAAMC,UAAU,GAAG,wBAAwB;;AAE3C;AACA,OAAO,MAAMC,kBAAkB,GAAGF,GAAG,CAACG,MAAM,CAAC;EAC3CC,IAAI,EAAEJ,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,+BAA+B,CAAC;EAC5DC,WAAW,EAAEP,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,yBAAyB,CAAC;EAC7DE,MAAM,EAAER,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,4BAA4B,CAAC;EAC3DG,OAAO,EAAET,GAAG,CAACK,MAAM,CAAC,CAAC;EAAE;EACvBK,IAAI,EAAEV,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CAAC;EAC/CK,KAAK,EAAEX,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,mBAAmB,CAAC;EACjDM,UAAU,EAAEZ,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,yBAAyB,CAAC;EAC5DO,WAAW,EAAEb,GAAG,CAACK,MAAM,CAAC,CAAC,CACtBS,OAAO,CAACb,UAAU,EAAE,mCAAmC,CAAC,CACxDK,QAAQ,CAAC,0BAA0B;AACxC,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMS,iBAAiB,GAAGf,GAAG,CAACG,MAAM,CAAC;EAC1Ca,IAAI,EAAEhB,GAAG,CAACK,MAAM,CAAC,CAAC,CACfY,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,4BAA4B,CAAC,CACnEX,QAAQ,CAAC,kBAAkB,CAAC;EAC/BY,SAAS,EAAElB,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,wBAAwB,CAAC;EAC1Da,QAAQ,EAAEnB,GAAG,CAACK,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,uBAAuB,CAAC;EACxDc,KAAK,EAAEpB,GAAG,CAACK,MAAM,CAAC,CAAC,CAChBe,KAAK,CAAC,oCAAoC,CAAC,CAC3Cd,QAAQ,CAAC,mBAAmB,CAAC;EAChCO,WAAW,EAAEb,GAAG,CAACK,MAAM,CAAC,CAAC,CACtBS,OAAO,CAACb,UAAU,EAAE,mCAAmC,CAAC,CACxDK,QAAQ,CAAC,0BAA0B,CAAC;EACvCe,WAAW,EAAErB,GAAG,CAACsB,IAAI,CAAC,CAAC,CACpBC,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC,EAAE,uCAAuC,CAAC,CACxDlB,QAAQ,CAAC,2BAA2B,CAAC;EACxCmB,MAAM,EAAEzB,GAAG,CAACK,MAAM,CAAC,CAAC,CACjBY,KAAK,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,wBAAwB,CAAC,CAC5DX,QAAQ,CAAC,oBAAoB;AAClC,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMoB,kBAAkB,GAAG,CAChC;EAAEC,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAW,CAAC,EACxC;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAM,CAAC,EAC9B;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAW,CAAC,EACxC;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,CACnC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG,CACzB;EAAEF,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAW,CAAC,EACxC;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAClC;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,CACnC;;AAED;AACA,OAAO,MAAME,aAAa,GAAG,CAC3B;EAAEH,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,EAChC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,CACnC;;AAED;AACA,OAAO,MAAMG,YAAY,GAAG,CAC1B;EAAEJ,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAS,CAAC,EAChC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAa,CAAC,EACpC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAc,CAAC,EACrC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAS,CAAC,EAChC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC/B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAO,CAAC,EAC9B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAS,CAAC,EAChC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAY,CAAC,EACnC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC/B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAgB,CAAC,EACvC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAY,CAAC,EACnC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAc,CAAC,EACrC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAS,CAAC,EAChC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAgB,CAAC,EACvC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAa,CAAC,EACpC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAa,CAAC,EACpC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAiB,CAAC,EACxC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAe,CAAC,EACtC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAO,CAAC,EAC9B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAS,CAAC,EAChC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAe,CAAC,EACtC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAe,CAAC,EACtC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAiB,CAAC,EACxC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAe,CAAC,EACtC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAY,CAAC,EACnC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC/B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAO,CAAC,EAC9B;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,EACjC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAW,CAAC,EAClC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAa,CAAC,EACpC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAgB,CAAC,EACvC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAY,CAAC,EACnC;EAAED,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAU,CAAC,CAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}