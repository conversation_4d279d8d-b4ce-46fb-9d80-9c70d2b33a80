{"name": "zen-observable", "version": "0.8.15", "repository": "zenparsing/zen-observable", "description": "An Implementation of ES Observables", "homepage": "https://github.com/zenparsing/zen-observable", "license": "MIT", "devDependencies": {"@babel/cli": "^7.6.0", "@babel/core": "^7.6.0", "@babel/preset-env": "^7.6.0", "@babel/register": "^7.6.0", "eslint": "^6.5.0", "mocha": "^6.2.0"}, "dependencies": {}, "scripts": {"test": "mocha --recursive --require ./scripts/mocha-require", "lint": "eslint src/*", "build": "git clean -dfX ./lib && node ./scripts/build", "prepublishOnly": "npm run lint && npm test && npm run build"}}